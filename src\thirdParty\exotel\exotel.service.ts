import { Injectable } from '@nestjs/common';
import { ExotelQuery } from './exotel.query';
import { raiseParamMissing } from 'src/config/error';
import { kActiveLoan, kRejectLoan } from 'src/constant/strings';
import { exotelCallbackWaitlistEntity } from 'src/database/pg/entities/exotelCallback.entity';
import { PgService } from 'src/database/pg/pg.service';
import { kSupportAdminIds } from 'src/constant/objects';
import { Env } from 'src/config/env';
import { kPathConsentToRecord } from 'src/constant/path';
import { UtilsService } from 'src/utils/utils.service';
import { CommonService } from 'src/common/common.services';
import { exotelWebhookData } from './exotel.interface';
import { ApiService } from 'src/utils/api.service';
import { exotelHeaders, exotelUserUrl } from 'src/constant/networks';

@Injectable()
export class ExotelService {
  constructor(
    private readonly query: ExotelQuery,
    private readonly pg: PgService,
    private readonly utils: UtilsService,
    private readonly commonService: CommonService,
    private readonly apiService: ApiService,
  ) {}

  async scheduleCallbackHandler(reqData) {
    if (!reqData?.CallFrom) raiseParamMissing('Caller Phone');
    const callerNum = this.utils.removeLeadingZero(reqData?.CallFrom);
    const data = await this.query.getCompleteUserDetails(callerNum);
    const loanData = data?.loanData;
    const masterData = data?.userData?.masterData;

    let adminId;
    if (loanData?.loanStatus != kActiveLoan && masterData?.assignedCSE)
      adminId = masterData?.assignedCSE;
    else if (loanData?.loanStatus == kActiveLoan) {
      if (loanData?.followerId) {
        adminId = loanData?.followerId;
      } else adminId = masterData?.assignedCSE;
    }

    const callbackPreference = JSON.parse(reqData?.digits);

    const payload: exotelWebhookData = {
      userId: data?.userData?.id,
      loanId: loanData?.id,
      assignedAdmin: adminId,
      callSId: reqData?.CallSid,
      callFrom: reqData?.CallFrom,
      callTo: reqData?.CallTo,
      callStartAt: new Date(reqData?.StartTime),
      callEndAt: new Date(reqData?.EndTime),
      callbackPreference,
      webhookReceivedAt: new Date(reqData?.CurrentTime),
      webhookResponse: reqData,
    };
    return await this.pg.create(exotelCallbackWaitlistEntity, payload);
  }

  async connectUserToAgent(reqData) {
    if (!reqData?.CallFrom) raiseParamMissing('Caller Phone');
    let userInput;

    const adminCompanyPhone = await this.getOrAssignAdmin(reqData?.CallFrom);
    if (reqData?.digits) userInput = JSON.parse(reqData?.digits);
    if (userInput == '3' || !adminCompanyPhone || !adminCompanyPhone.length)
      return await this.connectToSupport();
    return await this.validateExotelAdmin(adminCompanyPhone);
  }

  async getOrAssignAdmin(callerNum) {
    const userPhone = this.utils.removeLeadingZero(callerNum);
    const data = await this.query.getCompleteUserDetails(userPhone);
    const loanData = data?.loanData;
    const masterData = data?.userData?.masterData;

    let adminId;
    if (loanData?.loanStatus != kActiveLoan && masterData?.assignedCSE)
      adminId = masterData?.assignedCSE;
    else if (loanData?.loanStatus == kActiveLoan) {
      if (loanData?.followerId) {
        adminId = loanData?.followerId;
      } else adminId = masterData?.assignedCSE;
    }

    if (
      !adminId ||
      loanData?.loanStatus == kRejectLoan ||
      kSupportAdminIds.includes(adminId)
    )
      return null;

    const adminCompanyPhone =
      (await this.commonService.getAdminData(adminId))?.companyPhone || null;

    return adminCompanyPhone ? [`+91${adminCompanyPhone}`] : null;
  }

  async connectToSupport() {
    const supportNumbers = [
      Env.thirdParty.exotel.support_contact_1,
      Env.thirdParty.exotel.support_contact_2,
    ];
    return await this.validateExotelAdmin(supportNumbers, false);
  }

  async validateExotelAdmin(adminPhones: string[], allowFallback = true) {
    const dialNumbers = [];

    for (let i = 0; i < adminPhones?.length; i++) {
      const adminPhone = adminPhones[i];
      const queryParams = {
        fields: 'devices',
        'devices.contact_uri': adminPhone,
      };
      const exotelResponse = await this.apiService.get(
        exotelUserUrl,
        queryParams,
        exotelHeaders,
      );

      const exotelAdmin =
        exotelResponse?.response?.response[0]?.data?.devices[0];

      if (exotelAdmin?.available && exotelAdmin?.status == 'free')
        dialNumbers.push(adminPhone);
    }

    if (dialNumbers?.length) return await this.buildCallPayload(dialNumbers);
    if (allowFallback) return await this.connectToSupport();
    return await this.buildCallPayload(['']); // Fallback to agent busy....
  }

  buildCallPayload(number) {
    return {
      fetch_after_attempt: false,
      destination: {
        numbers: number,
      },
      outgoing_phone_number: Env.thirdParty.exotel.outgoing_number,
      record: true,
      recording_channels: 'dual',
      max_ringing_duration: 45,
      max_conversation_duration: 3600,
      music_on_hold: { type: 'default_tone' },
      start_call_playback: {
        playback_to: 'both',
        type: 'audio_url',
        value: kPathConsentToRecord,
      },
    };
  }
}
