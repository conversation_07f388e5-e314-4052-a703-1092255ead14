// Imports
import { LoanService } from './loan.service';
import { Body, Controller, Post } from '@nestjs/common';

@Controller('loan')
export class LoanController {
  constructor(private readonly service: LoanService) {}

  @Post('syncLoanDetails')
  async funSyncLoanDetails(@Body() body) {
    return await this.service.syncLoanDetails(body);
  }

  @Post('syncEmiDetails')
  async funSyncEmiDetails(@Body() body) {
    return await this.service.syncEmiDetails(body);
  }

  @Post('bulkSyncEmiDetails')
  async funBulkSyncEmiDetails() {
    return await this.service.bulkSyncEmiDetails();
  }
}
