// Imports
import { Modu<PERSON> } from '@nestjs/common';
import { UtilsModule } from 'src/utils/utils.module';
import { MigrationService } from './migration.service';
import { PgService } from 'src/database/pg/pg.service';
import { MigrationController } from './migration.controller';
import { MigrationQuery } from './migration.query';
import { UserModule } from '../user/user.module';
import { DataCodesQuery } from 'src/neighbours/data-codes/data.codes.query';
import { DataCodesService } from 'src/neighbours/data-codes/data.codes.service';

@Module({
  controllers: [MigrationController],
  imports: [UserModule, UtilsModule],
  providers: [
    DataCodesService,
    DataCodesQuery,
    MigrationService,
    MigrationQuery,
    PgService,
  ],
})
export class MigrationModule {}
