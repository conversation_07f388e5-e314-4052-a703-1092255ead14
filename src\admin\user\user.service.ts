// Imports
import { HttpStatusCode } from 'axios';
import { UserQuery } from './user.query';
import { Injectable } from '@nestjs/common';
import { getDataCodesAuth } from 'src/constant/auth';
import { nDataCodes } from 'src/constant/networks';
import { ApiService } from 'src/utils/api.service';
import { HTTPError } from 'src/config/error';

@Injectable()
export class UserService {
  constructor(
    private readonly api: ApiService,
    private readonly query: UserQuery,
  ) {}

  async calculateLeadScore(reqData) {
    const isReadOnly = reqData.readOnly == 'true' || reqData.readOnly == true;
    // Gathering -> Data
    const targetData = await this.query.dataForLeadCalculation(reqData);
    if (isReadOnly) {
      return { isReadOnly, targetData };
    }

    // Hitting -> API
    const headers = getDataCodesAuth();
    const response = await this.api.post(
      nDataCodes.leadScore,
      targetData,
      null,
      null,
      { headers },
    );
    if (response.status == HttpStatusCode.Ok) {
      return { success: true, lead_score: response.response.score };
    } else {
      throw HTTPError({ error: 'Error -> calculateLeadScore api' });
    }
  }
}
