pipeline {
    agent any
    environment {
        JENKINS_CONSOLE_LINK = "${env.JENKINS_URL}job/${env.JOB_NAME.split('/').join('/job/')}/${env.BUILD_NUMBER}/console"
        SCRIPT_PATH = "/home/<USER>/scripts/admin_backend_uat_deploy_.sh"
    }
    options {
        timeout(time: 30, unit: 'MINUTES')
    }
    stages {
        stage('Commit Info') {
            steps {
                script {
                    def commitMessage = sh(script: 'git log -1 --pretty=%B', returnStdout: true).trim()
                    def committerName = sh(script: 'git log -1 --pretty=%cn', returnStdout: true).trim()
                    def repoUrl = sh(script: 'git config --get remote.origin.url', returnStdout: true).trim()
                    def repoName = repoUrl.replaceFirst(/^.*[:\/]/, '').replaceAll('.git$', '') 
                    if (committerName == "GitHub") {
                        def originalAuthorName = sh(script: 'git log -1 --pretty=%an', returnStdout: true).trim()
                        committerName = originalAuthorName
                    }
                    env.COMMIT_MESSAGE = commitMessage
                    env.COMMIT_MESSAGE = env.COMMIT_MESSAGE.replaceAll(/[\'\"\\]/, '')  
                    env.COMMITTER_NAME = committerName
                    env.REPO_NAME = repoName  
                    env.BRANCH_NAME = env.GIT_BRANCH ?: sh(script: 'git rev-parse --abbrev-ref HEAD', returnStdout: true).trim()
                    env.HOSTNAME = sh(script: 'hostname', returnStdout: true).trim()                    
                }
            }
        }
        stage('Deploy') {
            steps {
                sh "bash ${SCRIPT_PATH}"
            }
        }
    }
    post {
        success {
            script {
                withCredentials([string(credentialsId: 'SLACK_TOKEN', variable: 'SLACK_TOKEN'),
                                 string(credentialsId: 'slackChannel', variable: 'SLACK_CHANNEL')]) {
                    sh """
                        curl --location 'https://slack.com/api/chat.postMessage' \
                             --header 'Authorization: Bearer ${SLACK_TOKEN}' \
                             --header 'Content-Type: application/json; charset=utf-8' \
                             --data '{
                                    "text": "*Update* -> New ${env.BRANCH_NAME} `${env.REPO_NAME}` (${env.HOSTNAME}) changes are deployed :rocket: \n*Committer Name:* `${env.COMMITTER_NAME}`\n*Commit Message:* ${env.COMMIT_MESSAGE}\n*Click to Check:* <${JENKINS_CONSOLE_LINK}|Click Here>",
                                    "channel": "${SLACK_CHANNEL}"}'
                    """
                }
            }
        }
        failure {
            script {
                withCredentials([string(credentialsId: 'SLACK_TOKEN', variable: 'SLACK_TOKEN'),
                                 string(credentialsId: 'slackChannel', variable: 'SLACK_CHANNEL')]) {
                    sh """
                        curl --location 'https://slack.com/api/chat.postMessage' \
                             --header 'Authorization: Bearer ${SLACK_TOKEN}' \
                             --header 'Content-Type: application/json; charset=utf-8' \
                             --data '{
                                    "text": "Build Failed :x: ${env.BRANCH_NAME} `${env.REPO_NAME}` (${env.HOSTNAME}) -> Please check the Jenkins pipeline for details. \n*Committer Name:* `${env.COMMITTER_NAME}` \n*Commit Message:* ${env.COMMIT_MESSAGE}\n*Click to Check:* <${JENKINS_CONSOLE_LINK}|Click Here>",
                                    "channel": "${SLACK_CHANNEL}"}'
                    """
                }
            }
        }
    }
}
