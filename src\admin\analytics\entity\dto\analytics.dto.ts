import { IsInt, IsString, IsOptional } from 'class-validator';
export class AnalyticsEntity {
  @IsOptional()
  @IsInt()
  id?: number;

  @IsInt()
  type: number; // 0-Bar,1-Pie,2-Box,3-Line

  @IsString()
  title: string;

  @IsOptional()
  @IsString()
  description: string;

  @IsOptional()
  @IsString()
  api: string;

  @IsOptional()
  @IsString()
  method: string;

  @IsOptional()
  @IsString()
  subtitle: string;

  @IsInt()
  row: number;

  @IsInt()
  column: number;

  @IsOptional()
  @IsInt()
  isActive: number;
}
