// Imports
import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { BankQuery } from './bank.query';
import { BankService } from './bank.service';
import { BankController } from './bank.controller';
import { ApiService } from 'src/utils/api.service';
import { PgService } from 'src/database/pg/pg.service';
import { TransactionsService } from 'src/neighbours/transactions/transactions.service';
import { ObjService } from 'src/utils/obj.service';

@Module({
  controllers: [BankController],
  providers: [
    ApiService,
    BankQuery,
    BankService,
    ObjService,
    PgService,
    TransactionsService,
  ],
})
export class BankModule {}
