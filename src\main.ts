// Imports
import * as express from 'express';
import { Env } from './config/env';
import { AppModule } from './app.module';
import { NestFactory } from '@nestjs/core';
import { kServerStr } from './constant/objects';
import { CODE_VERSION } from './app.service';
import { ValidationPipe } from '@nestjs/common';
import { Logger } from '@nestjs/common';

async function bootstrap() {
  const logger = new Logger('Server');
  logger.log(kServerStr.nestJsInit);

  const app = await NestFactory.create(AppModule);

  app.enableCors();
  app.use(express.json({ limit: '100mb' }));
  app.use(express.urlencoded({ limit: '100mb', extended: true }));
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
    }),
  );
  await app.listen(Env.server.port);

  logger.log(`${kServerStr.runningPort}${Env.server.port}`);
  logger.log(`Code version -> ${CODE_VERSION}`);
}

bootstrap();
