import { Modu<PERSON> } from '@nestjs/common';
import { AnalyticsService } from './analytics.service';
import { AnalyticsController } from './analytics.controller';
import { ClickHouseService } from 'src/database/clickhouse/clickhouse.service';
import { CommonService } from 'src/utils/comman.service';
import { CreateQuery } from 'src/createTable/createQuery';
import { PgService } from 'src/database/pg/pg.service';
import { RedisModule } from 'src/database/redis/redis.module';
import { PgModule } from 'src/database/pg/pg.module';
@Module({
  imports: [RedisModule, PgModule],
  controllers: [AnalyticsController],
  providers: [
    AnalyticsService,
    ClickHouseService,
    CommonService,
    CreateQuery,
    PgService,
  ],
})
export class AnalyticsModule {}
