// Imports
import { Controller, Get } from '@nestjs/common';
import { PaymentService } from './payment.service';

@Controller('payment')
export class PaymentController {
  constructor(private readonly service: PaymentService) {}

  @Get('forecast')
  async funForecast() {
    return await this.service.forecast();
  }

  @Get('as-on-date-recovery')
  async funAsOnDateRecovery() {
    return await this.service.asOnDateRecovery();
  }
}
