// Imports
import { Includeable } from 'sequelize';
import { Injectable } from '@nestjs/common';
import { PgService } from 'src/database/pg/pg.service';
import { KYCEntity } from 'src/database/pg/entities/kyc.entity';
import { raiseBadRequest, raiseParamMissing } from 'src/config/error';
import { registeredUsers } from 'src/database/pg/entities/registeredUsers';
import { MasterEntity } from 'src/database/pg/entities/master.entity';

@Injectable()
export class UserQuery {
  constructor(private readonly pg: PgService) {}

  async dataForLeadCalculation(reqData) {
    const userId = reqData.userId;
    if (!userId) {
      raiseParamMissing('userId');
    }

    const kycInclude: Includeable = { model: KYCEntity };
    kycInclude.attributes = ['aadhaarDOB', 'aadhaarState'];

    const masterInclude: Includeable = { model: MasterEntity };
    masterInclude.attributes = ['otherInfo'];

    const include: Includeable[] = [kycInclude, masterInclude];

    const userData = await this.pg.findOne(registeredUsers, {
      afterFindOptions: { aadhaarState: true, dobInYears: true },
      include,
      where: { id: userId },
    });
    if (!userData) {
      raiseBadRequest('No user data found');
    }

    const otherInfo = userData.masterData?.otherInfo ?? {};
    const userEnteredSalary = otherInfo?.salaryInfo ?? otherInfo?.netPaySalary;
    const educationInfo = otherInfo?.educationInfo ?? '';
    const residentialInfo = otherInfo?.residentialInfo ?? '';
    const employmentInfo = otherInfo?.employmentInfo ?? '';

    return {
      aadhaar_state: userData.kycData?.aadhaarState,
      age: userData.kycData?.dobInYears,
      education_info: educationInfo,
      employment_info: employmentInfo,
      residential_info: residentialInfo,
      salary: userEnteredSalary.toString(),
      user_id: userId,
    };
  }
}
