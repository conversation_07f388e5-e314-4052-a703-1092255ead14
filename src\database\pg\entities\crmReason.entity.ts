import {
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
} from 'sequelize-typescript';
import { Department } from './department';

@Table({})
export class CrmReasonEntity extends Model<CrmReasonEntity> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;
  @Column({
    type: DataType.TEXT,
    allowNull: false,
    unique: true,
  })
  reason: string;
  @ForeignKey(() => Department)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  departmentId: number;

  @BelongsTo(() => Department, {
    foreignKey: 'departmentId',
    targetKey: 'id',
  })
  departmentData: Department;
}
