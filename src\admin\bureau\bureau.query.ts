// Imports
import { Includeable, Op } from 'sequelize';
import { Injectable } from '@nestjs/common';
import { HTTPError, raiseNotFound, raiseParamMissing } from 'src/config/error';
import { PgService } from 'src/database/pg/pg.service';
import { EmiEntity } from 'src/database/pg/entities/emi.entity';
import { ISequelizeFindOptions } from 'src/database/pg/pg.intereface';
import { registeredUsers } from 'src/database/pg/entities/registeredUsers';
import { loanTransaction } from 'src/database/pg/entities/loanTransaction';
import { ExperianScoreEntity } from 'src/database/pg/entities/experian.entity';

@Injectable()
export class BureauQuery {
  constructor(private readonly pg: PgService) {}

  async dataForBulkPull(
    reqData,
  ): Promise<{ count: number; rows: { uniqueId: number; userId: string }[] }> {
    const target: 'UPCOMING_EMI' = reqData.target;
    if (!target) raiseParamMissing('target');

    if (target == 'UPCOMING_EMI') {
      return await this.upcomingEmiForBulkPull(reqData);
    } else if (target == 'ACTIVE_DEFAULTERS') {
      return await this.activeDefaultersForBulkPull(reqData);
    } else if (target == 'LOAN_CLOSED') {
      return await this.loanClosedForBulkPull(reqData);
    } else if (target == 'ON_DEMAND') {
      return await this.onDemandForBulkPull(reqData);
    }

    throw HTTPError({});
  }

  async dataForSoftEligibility(userId) {
    if (!userId) {
      raiseParamMissing('userId');
    }

    const experianData: ExperianScoreEntity = await this.pg.findOne(
      ExperianScoreEntity,
      {
        attributes: ['experianScore', 'formattedResponse', 'overdueAccounts'],
        where: { userId },
      },
    );
    if (!experianData) {
      raiseNotFound();
    }

    const formattedResponse = experianData.formattedResponse ?? {
      INProfileResponse: {},
    };
    return {
      response: formattedResponse?.INProfileResponse ?? {},
      experianScore: experianData.experianScore,
      overdueAccounts: experianData.overdueAccounts,
    };
  }

  private async upcomingEmiForBulkPull(reqData) {
    const start_date = reqData.start_date;
    if (!start_date) raiseParamMissing('start_date');
    const end_date = reqData.end_date;
    if (!end_date) raiseParamMissing('end_date');

    const userInc: Includeable = { model: registeredUsers };
    userInc.attributes = ['uniqueId'];
    if (reqData.sub_target == 'ONTIME') {
      userInc.where = { loanStatus: 1 };
    }

    const options: ISequelizeFindOptions = {
      attributes: ['userId'],
      include: userInc,
      where: {
        emi_date: { [Op.between]: [start_date, end_date] },
        payment_status: '0',
      },
    };
    if (reqData.limit) options.limit = reqData.limit;

    const emiList = await this.pg.findAll(EmiEntity, options);

    // Fine tuning -> Response
    const finalizedList = [];
    emiList.forEach((el) => {
      finalizedList.push({ uniqueId: el?.user?.uniqueId, userId: el.userId });
    });
    return { count: finalizedList.length, rows: finalizedList };
  }

  private async activeDefaultersForBulkPull(reqData) {
    const start_date = reqData.start_date;
    if (!start_date) raiseParamMissing('start_date');
    const end_date = reqData.end_date;
    if (!end_date) raiseParamMissing('end_date');

    const options: ISequelizeFindOptions = {
      attributes: ['id', 'uniqueId'],
      where: {
        createdAt: { [Op.between]: [start_date, end_date] },
        loanStatus: '3',
      },
    };
    if (reqData.limit) options.limit = reqData.limit;
    const userList = await this.pg.findAll(registeredUsers, options);

    const finalizedList = [];
    userList.forEach((el) => {
      finalizedList.push({ uniqueId: el.uniqueId, userId: el.id });
    });

    return { count: finalizedList.length, rows: finalizedList };
  }

  private async loanClosedForBulkPull(reqData) {
    const start_date = reqData.start_date;
    if (!start_date) raiseParamMissing('start_date');
    const end_date = reqData.end_date;
    if (!end_date) raiseParamMissing('end_date');

    const userInc: Includeable = { model: registeredUsers };
    userInc.attributes = ['uniqueId'];
    if (reqData.sub_target == 'ONTIME') {
      userInc.where = { loanStatus: 1 };
    }

    const options: ISequelizeFindOptions = {
      attributes: ['userId'],
      include: userInc,
      where: {
        loanCompletionDate: { [Op.between]: [start_date, end_date] },
      },
    };
    if (reqData.limit) options.limit = reqData.limit;

    const loanList = await this.pg.findAll(loanTransaction, options);

    // Fine tuning -> Response
    const finalizedList = [];
    loanList.forEach((el) => {
      finalizedList.push({
        uniqueId: el?.registeredUsers?.uniqueId,
        userId: el.userId,
      });
    });
    return { count: finalizedList.length, rows: finalizedList };
  }

  private async onDemandForBulkPull(reqData) {
    const userIds = reqData.userIds;
    if (!userIds || userIds.length == 0) raiseParamMissing('userIds');

    const options: ISequelizeFindOptions = {
      attributes: ['id', 'uniqueId'],
      where: { id: userIds },
    };
    const userList = await this.pg.findAll(registeredUsers, options);

    const finalizedList = [];
    userList.forEach((el) => {
      finalizedList.push({ userId: el.id, uniqueId: el.uniqueId });
    });

    return { count: finalizedList.length, rows: finalizedList };
  }
}
