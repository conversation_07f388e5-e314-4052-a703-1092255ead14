// Imports
import { Injectable } from '@nestjs/common';
import { BureauQuery } from './bureau.query';
import { DateService } from 'src/utils/date.service';
import {
  DATA_CODES_CLICKHOUSE_TABLE,
  EXPERIAN_GOOD_COMPANIES,
} from 'src/constant/objects';
import { BackendServiceService } from 'src/neighbours/backend-service/backend.service.service';
import { ApiService } from 'src/utils/api.service';
import { nDataCodes } from 'src/constant/networks';
import { getDataCodesAuth } from 'src/constant/auth';

@Injectable()
export class BureauService {
  constructor(
    private readonly api: ApiService,
    private readonly query: BureauQuery,
    private readonly dateService: DateService,
    private readonly backendService: BackendServiceService,
  ) {}

  async bulkPull(reqData) {
    const isReadOnly = reqData.readOnly == 'true' || reqData.readOnly == true;

    const data = await this.query.dataForBulkPull(reqData);
    if (isReadOnly) {
      return data;
    }

    if (reqData.type == 'EXPERIAN') {
      for (let index = 0; index < data.rows.length; index++) {
        await this.backendService.experianSoftHit(
          data.rows[index].uniqueId.toString(),
        );
      }
    }

    return { data };
  }

  async predictSoftEligibility(reqData) {
    const experianData = await this.query.dataForSoftEligibility(
      reqData.userId,
    );
    if (experianData.experianScore < 650) {
      return {
        success: true,
        isSoftEligible: false,
        reason: 'EXPERIAN_SCORE_BELOW_650',
        values: { experianScore: experianData.experianScore },
      };
    }
    if (experianData.overdueAccounts > 0) {
      return {
        success: true,
        isSoftEligible: false,
        reason: 'EXPERIAN_OVERDUE_FOUND',
        values: { overdueAccounts: experianData.overdueAccounts },
      };
    }

    const accounts =
      experianData?.response?.CAIS_Account?.CAIS_Account_DETAILS ?? [];
    const today = this.dateService.getTodayGlobalDate();

    let isGoodAccFound = false;
    let total_accounts = 0;
    for (let index = 0; index < accounts.length; index++) {
      const accData = accounts[index];
      const openDate = this.dateService.strToDate(
        accData.Open_Date,
        'YYYYMMDD',
      );
      const dateDiff = this.dateService.difference(openDate, today);
      if (dateDiff <= 62) {
        const subscriber_name: string = (
          accData.Subscriber_Name ?? ''
        ).toUpperCase();
        if (EXPERIAN_GOOD_COMPANIES.includes(subscriber_name)) {
          isGoodAccFound = true;
          total_accounts++;
        }
      }
    }

    if (!isGoodAccFound) {
      return {
        success: true,
        isSoftEligible: false,
        reason: 'NO_GOOD_ACC_FOUND_IN_62_DAYS',
        values: { total_accounts },
      };
    }

    await this.api.post(
      nDataCodes.bulkWrites,
      {
        db_type: 'clickHouse',
        table_name: DATA_CODES_CLICKHOUSE_TABLE.experian_soft_eligible,
        objects: [
          {
            user_id: reqData.userId,
          },
        ],
      },
      null,
      null,
      { headers: getDataCodesAuth() },
    );

    return {
      success: true,
      isSoftEligible: true,
      reason: 'GOOD_ACC_FOUND_IN_62_DAYS',
      values: { total_accounts },
    };
  }
}
