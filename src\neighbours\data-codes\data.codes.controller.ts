// Imports
import { DataCodesService } from './data.codes.service';
import { Body, Controller, Get, Post } from '@nestjs/common';

@Controller('data-codes')
export class DataCodesController {
  constructor(private readonly service: DataCodesService) {}

  @Post('calculateInternalScore')
  async funCalculateInternalScore(@Body() body) {
    return await this.service.calculateInternalScore(body);
  }

  @Post('syncEMIDetails')
  async funSyncEMIDetails(@Body() body) {
    return await this.service.syncEMIDetails(body);
  }

  // Bulk api of syncEMIDetails From 1st jan 24 to yesterday (Chunk wise -> Chunk of 10 Days)
  @Post('syncAllEMIDetails')
  async funSyncAllEMIDetails() {
    return await this.service.syncAllEMIDetails();
  }

  @Post('syncCIBILDetails')
  async funSyncCIBILDetails() {
    return await this.service.syncCIBILDetails();
  }

  @Get('testList')
  async funTestList() {
    return this.service.testList();
  }

  @Post('injectReadOnlyQuery')
  async funInjectReadOnlyQuery(@Body() body) {
    return await this.service.injectReadOnlyQuery(body);
  }
}
