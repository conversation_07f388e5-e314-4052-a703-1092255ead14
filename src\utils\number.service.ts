import { Injectable } from '@nestjs/common';

@Injectable()
export class NumberService {
  private readonly ones = [
    '',
    'One',
    'Two',
    'Three',
    'Four',
    'Five',
    'Six',
    'Seven',
    'Eight',
    'Nine',
    'Ten',
    'Eleven',
    'Twelve',
    'Thirteen',
    'Fourteen',
    'Fifteen',
    'Sixteen',
    'Seventeen',
    'Eighteen',
    'Nineteen',
  ];
  private readonly tens = [
    '',
    '',
    'Twenty',
    'Thirty',
    'Forty',
    'Fifty',
    'Sixty',
    'Seventy',
    'Eighty',
    'Ninety',
  ];

  constructor() {}

  withCommas(x, rupeeSymbol = false) {
    const withCommaStr = this.amountNumberWithCommas(x);
    if (rupeeSymbol) {
      return '₹' + withCommaStr;
    }
    return withCommaStr;
  }

  private amountNumberWithCommas(x) {
    try {
      let amount = typeof x != 'string' ? x.toString() : x;
      if (amount.includes('.')) amount = (+amount).toFixed(2);
      if (amount.length < 6)
        return amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      else {
        amount = amount.toString().replace(/\B(?=(\d{2})+(?!\d))/g, ',');
        let tempAmount = '';
        let isCommas = false;
        for (let index = amount.length - 1; index >= 0; index--) {
          const element = amount[index];
          if (element.includes('L')) continue;
          if (element == ',') isCommas = true;
          else if (isCommas) {
            isCommas = false;
            tempAmount += element + ',';
          } else tempAmount += element;
        }
        let finalAmount = '';
        for (let index = tempAmount.length - 1; index >= 0; index--) {
          const element = tempAmount[index];
          finalAmount += element;
        }
        if (finalAmount.startsWith(','))
          finalAmount = finalAmount.replace(',', '');
        return finalAmount;
      }
    } catch (error) {
      return x;
    }
  }

  convertFloatToWords(num: number): string {
    const target_num = num.toString();
    const spans = target_num.split('.');

    let final_words = '';
    const final_list = [];
    spans.forEach((el) => {
      const words = this.convertNumberToWords(+el);
      final_list.push(words);
    });

    final_words = final_list.join(' Point ');

    return final_words.trim();
  }

  convertNumberToWords(num: number): string {
    if (num === 0) return 'zero';

    const twoDigits = (num: number): string => {
      if (num < 20) {
        return this.ones[num];
      } else {
        return (
          this.tens[Math.floor(num / 10)] +
          (num % 10 !== 0 ? ' ' + this.ones[num % 10] : '')
        );
      }
    };

    const threeDigits = (num: number): string => {
      if (num === 0) {
        return '';
      } else if (num < 100) {
        return twoDigits(num);
      } else {
        return (
          this.ones[Math.floor(num / 100)] +
          ' Hundred' +
          (num % 100 !== 0 ? ' ' + twoDigits(num % 100) : '')
        );
      }
    };

    const parts: string[] = [];
    const crore = Math.floor(num / 10000000);
    if (crore) {
      parts.push(threeDigits(crore) + ' Crore');
    }
    num %= 10000000;
    const lakh = Math.floor(num / 100000);
    if (lakh) {
      parts.push(threeDigits(lakh) + ' Lakh');
    }
    num %= 100000;
    const thousand = Math.floor(num / 1000);
    if (thousand) {
      parts.push(threeDigits(thousand) + ' Thousand');
    }
    num %= 1000;
    const hundredAndBelow = threeDigits(num);
    if (hundredAndBelow) {
      parts.push(hundredAndBelow);
    }

    return parts.join(' ');
  }
}
