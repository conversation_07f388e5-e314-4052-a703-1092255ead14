import { Inject, Injectable } from '@nestjs/common';
import { RedisClientType } from 'redis';
import { Env } from 'src/config/env';
import { kRedisClient } from 'src/constant/strings';

@Injectable()
export class RedisService {
  constructor(
    @Inject(kRedisClient)
    private readonly redisClient: RedisClientType,
  ) {}

  set(key: string, value: string, ttl?: number): Promise<string> {
    return this.redisClient.set(Env.database.redis.prefix + key, value, {
      EX: ttl,
    });
  }

  get(key: string): Promise<string | null> {
    return this.redisClient.get(Env.database.redis.prefix + key);
  }

  delKey(key: string): Promise<number> {
    return this.redisClient.del(Env.database.redis.prefix + key);
  }

  async setAdd(key: string, members: string[], ttl?: number): Promise<void> {
    await this.redisClient.sAdd(Env.database.redis.prefix + key, members);
    if (ttl)
      await this.redisClient.expire(Env.database.redis.prefix + key, ttl);
  }

  setRem(key: string, members: string[]): Promise<number> {
    return this.redisClient.sRem(Env.database.redis.prefix + key, members);
  }

  setIsMember(key: string, member: string): Promise<boolean> {
    return this.redisClient.sIsMember(Env.database.redis.prefix + key, member);
  }

  setGetMembers(key: string): Promise<string[]> {
    return this.redisClient.sMembers(Env.database.redis.prefix + key);
  }

  //get all key like *key*
  keyList(keyLike: string): Promise<string[]> {
    const filter: string = `*${keyLike}*`;
    return this.redisClient.sendCommand(['KEYS', filter]); // 'OK'
  }

  //delete all key like *key*
  async keyDelete(keyLike: string): Promise<number> {
    const keysList: string[] = await this.keyList(keyLike);
    if (keysList?.length)
      return this.redisClient.sendCommand(['DEL', ...keysList]);
    return 0;
  }

  async setIfNotExistsWithNX(key: string, ttl: number): Promise<boolean> {
    const isSet: boolean = await this.redisClient.setNX(
      Env.database.redis.prefix + key,
      'Exists !',
    );
    if (isSet)
      await this.redisClient.expire(Env.database.redis.prefix + key, ttl);
    return isSet;
  }
}
