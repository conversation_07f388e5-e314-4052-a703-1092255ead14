FROM --platform=linux/amd64 ubuntu:20.04

RUN apt-get update && apt-get install -y curl

RUN curl -sL https://deb.nodesource.com/setup_18.x | bash - \
            && apt-get install -y nodejs

ENV TZ=Asia/Kolkata

RUN npm i -g @nestjs/cli@9.0.0

WORKDIR /app

COPY package.json .

RUN npm install --force

RUN npm install typescript@5.4 --force

COPY . .

RUN npm run build

CMD ["npm","run","start:prod"]
