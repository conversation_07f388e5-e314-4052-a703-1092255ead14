import { Injectable, OnModuleInit } from '@nestjs/common';
import { kSystem, SYSTEM_ADMIN_ID } from 'src/constant/global';
import { admin } from 'src/database/pg/entities/admin.entity';
import { PgService } from 'src/database/pg/pg.service';
import { decryptText } from 'src/utils/crypt';

@Injectable({})
export class CommonService implements OnModuleInit {
  adminList: Array<{
    id: number;
    fullName: string;
    phone: string;
    email: string;
    companyPhone: string;
  }> = [];
  constructor(private readonly pg: PgService) {}

  async onModuleInit() {
    this.getAdminData(1);
  }

  async getAdminData(filterByKey: number | string) {
    if (filterByKey == SYSTEM_ADMIN_ID || !filterByKey)
      return {
        phone: '',
        email: '',
        id: filterByKey,
        companyPhone: '',
        fullName: kSystem,
      };

    if (this.adminList.length > 0) {
      const findData = this.adminList.find(
        (admin) =>
          admin.id == filterByKey ||
          admin.phone == filterByKey ||
          admin.email == filterByKey ||
          admin.fullName == filterByKey ||
          admin.companyPhone == filterByKey,
      );
      if (findData) return findData;
      else this.adminList = [];
    } else {
      const attr = ['id', 'fullName', 'email', 'phone', 'companyPhone'];
      const options = {
        attributes: attr,
        where: {},
      };
      const admins = await this.pg.findAll(admin, options);
      if (!admins || admins.length == 0) this.adminList = [];
      else {
        for (let i = 0; i < admins.length; i++) {
          admins[i].email = admins[i]?.email && decryptText(admins[i]?.email);
          admins[i].phone = admins[i]?.phone && decryptText(admins[i]?.phone);
          admins[i].companyPhone =
            admins[i]?.companyPhone && decryptText(admins[i]?.companyPhone);
        }

        this.adminList = admins;
        const findData = this.adminList.find(
          (admin) =>
            admin.id == filterByKey ||
            admin.phone == filterByKey ||
            admin.email == filterByKey ||
            admin.fullName == filterByKey ||
            admin.companyPhone == filterByKey,
        );

        if (findData) return findData;
      }
    }
    return {};
  }
}
