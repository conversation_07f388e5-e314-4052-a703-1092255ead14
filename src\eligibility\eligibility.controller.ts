// Imports
import { Body, Controller, Post } from '@nestjs/common';
import { EligibilityService } from './eligibility.service';

@Controller('eligibility')
export class EligibilityController {
  constructor(private readonly service: EligibilityService) {}

  @Post('checkPreApprovalStatus')
  async funCheckPreApprovalStatus(@Body() body) {
    return await this.service.checkPreApprovalStatus(body);
  }
}
