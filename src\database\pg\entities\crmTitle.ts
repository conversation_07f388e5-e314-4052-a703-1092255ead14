import {
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
} from 'sequelize-typescript';
import { crmDisposition } from './crmDisposition';
import { crmStatus } from './crmStatus';
import { Department } from './department';

@Table({})
export class crmTitle extends Model<crmTitle> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ForeignKey(() => Department)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  departmentId: number;

  @BelongsTo(() => Department, {
    foreignKey: 'departmentId',
    targetKey: 'id',
  })
  departmentData: Department;

  @ForeignKey(() => crmStatus)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  crmStatusId: number;

  @BelongsTo(() => crmStatus, {
    foreignKey: 'crmStatusId',
    targetKey: 'id',
  })
  crmStatusData: crmStatus;

  @ForeignKey(() => crmDisposition)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  crmDispositionId: number;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
    unique: true,
  })
  title: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
  })
  isAmount: boolean;
  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
  })
  isDate: boolean;
  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
  })
  isReference: boolean;
  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
  })
  isReason: boolean;
  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
  })
  isSettlement: boolean;
  @Column({
    type: DataType.ENUM,
    values: ['0', '1'],
    defaultValue: '0',
  })
  loanStatus: string;
  @Column({
    type: DataType.ARRAY(DataType.INTEGER),
    allowNull: true,
    defaultValue: [],
  })
  departmentIds: number;

  @ForeignKey(() => crmDisposition)
  @Column({
    type: DataType.ARRAY(DataType.INTEGER),
    allowNull: true,
    defaultValue: [],
  })
  crmDispositionIds: number;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  tempData: any;
}
