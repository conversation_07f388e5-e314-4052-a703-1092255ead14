export interface exotelWebhookData {
  userId: string;
  callTo: string;
  loanId: number;
  callEndAt: Date;
  callSId: string;
  callFrom: string;
  callStartAt: Date;
  assignedAdmin: number;
  webhookReceivedAt: Date;
  webhookResponse: object;
  callbackPreference: string;
}

export interface callbackReportQuery {
  page: string;
  startDate: Date;
  endDate: Date;
  isCalled: string;
  callbackSlot: string;
}
