// Imports
import { Injectable } from '@nestjs/common';
import { DataCodesService } from 'src/neighbours/data-codes/data.codes.service';

@Injectable()
export class PaymentService {
  constructor(private readonly dataCodes: DataCodesService) {}

  async forecast() {
    const response = await this.dataCodes.paymentForecast();
    return { success: true, data: response };
  }

  async asOnDateRecovery() {
    const response = await this.dataCodes.asOnDateRecovery();
    return { success: true, data: response };
  }
}
