// Imports
import { Injectable } from '@nestjs/common';
import { ENUM_DATA } from 'src/constant/objects';
import { PgService } from 'src/database/pg/pg.service';
import { ISequelizeFindOptions } from 'src/database/pg/pg.intereface';
import { loanTransaction } from 'src/database/pg/entities/loanTransaction';

@Injectable()
export class CommunicationQuery {
  constructor(private readonly pg: PgService) {}

  async dataForMaintenanceAlert(reqData) {
    const target_audiance: 'ACTIVE_LOANS' = reqData.target_audiance;
    const audiance_limit = reqData.audiance_limit;

    let loanWhere = { loanStatus: undefined };
    if (target_audiance == 'ACTIVE_LOANS') {
      loanWhere.loanStatus = ENUM_DATA.ACTIVE_LOAN;
    }

    const loanOptions: ISequelizeFindOptions = {
      attributes: ['id'],
      where: loanWhere,
      limit: audiance_limit,
    };
    const loanList = await this.pg.findAll(loanTransaction, loanOptions);

    return { loanList };
  }
}
