// Imports
import { BureauService } from './bureau.service';
import { Body, Controller, Post } from '@nestjs/common';

@Controller('bureau')
export class BureauController {
  constructor(private readonly service: BureauService) {}

  @Post('bulkPull')
  async funBulkPull(@Body() body) {
    return await this.service.bulkPull(body);
  }

  @Post('predictSoftEligibility')
  async funPredictSoftEligibility(@Body() body) {
    return await this.service.predictSoftEligibility(body);
  }
}
