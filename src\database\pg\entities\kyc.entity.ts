// Imports
import {
  Table,
  Column,
  Model,
  DataType,
  AfterFind,
} from 'sequelize-typescript';
import { DateService } from 'src/utils/date.service';

@Table({})
export class KYCEntity extends Model<KYCEntity> {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
    allowNull: false,
  })
  id: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  aadhaarNo: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
  })
  aadhaarState: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  aadhaarDOB: string;

  @AfterFind
  static formatAfterFind(instances, options: any) {
    if (!options.afterFindOptions) return;

    // Helps to identify correct aadhaarState
    if (options.afterFindOptions?.aadhaarState == true) {
      // If multiple records are found (findAll)
      if (Array.isArray(instances)) {
      } // Single record (findOne)
      else if (instances) {
      }
    }

    // Helps to convert different DOB format into one and calculate age
    if (options.afterFindOptions?.dobInYears == true) {
      if (instances && instances.aadhaarDOB) {
        const aadhaarDOB = DateService.aadhaarDateStrToDOBDate(
          instances.aadhaarDOB,
        );
        instances.dobInYears = DateService.difference(
          new Date(),
          aadhaarDOB,
          'Years',
        );
      }
    }
  }
}
