// Imports
import { Module } from '@nestjs/common';
import { UtilsModule } from 'src/utils/utils.module';
import { PgModule } from 'src/database/pg/pg.module';
import { EligibilityQuery } from './eligibility.query';
import { EligibilityService } from './eligibility.service';
import { EligibilityController } from './eligibility.controller';

@Module({
  controllers: [EligibilityController],
  imports: [PgModule, UtilsModule],
  providers: [EligibilityService, EligibilityQuery],
})
export class EligibilityModule {}
