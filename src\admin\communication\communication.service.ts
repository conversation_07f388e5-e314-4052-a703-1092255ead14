// Imports
import { Injectable } from '@nestjs/common';
import { CommunicationQuery } from './communication.query';
import { MicroAlertService } from 'src/neighbours/micro-alert/micro.alert.service';

@Injectable()
export class CommunicationService {
  constructor(
    private readonly microAlert: MicroAlertService,
    private readonly query: CommunicationQuery,
  ) {}

  async maintenanceAlert(reqData) {
    const data = await this.query.dataForMaintenanceAlert(reqData);

    return await this.microAlert.sendWhatsAppMsg({ data });
  }
}
