// Imports
import { HttpStatus, Injectable } from '@nestjs/common';
import { ApiService } from 'src/utils/api.service';
import { nTransactions, nTrueShield } from 'src/constant/networks';
import { HTTPError } from 'src/config/error';
import { ObjService } from 'src/utils/obj.service';
import { Env } from 'src/config/env';

@Injectable()
export class TransactionsService {
  constructor(
    private readonly api: ApiService,
    private readonly obj: ObjService,
  ) {}

  async getInsights(reqData) {
    const accountNumber = reqData.accountNumber;
    let queryParam = `?salaryAccountId=${accountNumber}`;
    const excludedParams: string[] = reqData.excludedParams ?? [];
    // Need new fresh tagging from Trueshield.Ai
    const refreshTsTagging = reqData.refreshTsTagging == true;
    const returnWrongTags = reqData.returnWrongTags == true;

    const bankCode = reqData.bankCode;
    if (bankCode) queryParam += `&bankCode=${bankCode}`;

    queryParam = encodeURI(queryParam);
    const url = nTransactions.getCompareAccounts + queryParam;

    const response = await this.api.get(url);
    const status = response.status;
    if (status == HttpStatus.OK) {
      const apiResponse = response.response ?? {};

      let finalizedResponse = apiResponse;
      for (let index = 0; index < excludedParams.length; index++) {
        finalizedResponse = this.obj.deleteKeyFromJson(
          apiResponse,
          excludedParams[index],
        );
      }

      // Fresh tagging
      if (refreshTsTagging) {
        const transactions = finalizedResponse.transactionJson ?? [];
        const input_transactions = [];
        transactions.forEach((el) => {
          delete el.category;
          input_transactions.push(el);
        });
        const body = { chinmay: 1, transactions: input_transactions };
        const headers = {
          'Authentication-Key': Env.neighbours.trueShield.auth_key,
        };
        const response = await this.api.post(
          nTrueShield.predictStatement,
          body,
          headers,
        );
        if (response.response.data) {
          const res = response.response.data ?? [];
          const other_output = res.other_output ?? [];

          // Purpose -> Improving the transaction category
          if (returnWrongTags) {
            const wrongTags = [];
            for (let index = 0; index < other_output.length; index++) {
              const transData = other_output[index];
              const subTag = transData.final_subtag ?? '-';
              if (subTag == '-' && transData.prediction1 == 'shopping') {
                wrongTags.push({
                  category: transData.prediction1,
                  description: transData.description,
                  amount: transData.amount,
                });
              }
            }

            return { wrongTags };
          } else {
            const finalizedList = [];
            other_output.forEach((el) => {
              if (excludedParams.includes('adminName')) {
                delete el.adminName;
              }
              if (excludedParams.includes('categoryDetails')) {
                delete el.categoryDetails;
              }
              if (excludedParams.includes('id')) {
                delete el.id;
              }
              if (typeof el.matched_keyword == 'string') {
                el.matched_keyword = [el.matched_keyword];
              }

              el.loanId = reqData.loanId;
              finalizedList.push(el);
            });

            return {
              loanId: reqData.loanId, // To link transactions based on loanId (Helps in bulk transactions)
              transactions: finalizedList,
            };
          }
        }
      }

      finalizedResponse.loanId = reqData.loanId; // To link transactions based on loanId (Helps in bulk transactions)
      return finalizedResponse;
    } else {
      throw HTTPError({
        message: 'Error while operation -> TransactionsService.getInsights',
      });
    }
  }
}
