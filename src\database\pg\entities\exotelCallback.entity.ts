import {
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
} from 'sequelize-typescript';

import { registeredUsers } from './registeredUsers';
import { loanTransaction } from './loanTransaction';
import { admin } from './admin.entity';

@Table({})
export class exotelCallbackWaitlistEntity extends Model<exotelCallbackWaitlistEntity> {
  @Column({
    type: DataType.STRING,
    allowNull: false,
    primaryKey: true,
  })
  callSId: string;

  @ForeignKey(() => registeredUsers)
  @Column({
    type: DataType.UUID,
    allowNull: true,
  })
  userId: string;

  @ForeignKey(() => loanTransaction)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  loanId: number;

  @ForeignKey(() => admin)
  @Column({
    type: DataType.SMALLINT,
    allowNull: true,
  })
  assignedAdmin: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  callFrom: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  callTo: string;

  @Column({
    type: DataType.DATE,
    allowNull: false,
  })
  callStartAt: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
  })
  callEndAt: Date;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: '1 -> 10AM to 12PM, 2 -> 2PM to 4PM, 3 -> 4PM to 6PM ',
  })
  callbackPreference: string;

  @Column({
    type: DataType.SMALLINT,
    allowNull: true,
    defaultValue: 0,
    comment: '0 -> Not called back, 1 -> Called back,',
  })
  callStatus: number;

  @Column({
    type: DataType.DATE,
    allowNull: false,
  })
  webhookReceivedAt: Date;

  @Column({
    type: DataType.JSONB,
    allowNull: false,
  })
  webhookResponse: object;
}
