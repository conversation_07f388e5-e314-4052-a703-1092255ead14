// Imports
import { AppService } from './app.service';
import { Body, Controller, Get, Post, Query } from '@nestjs/common';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  getHello() {
    return this.appService.getHello();
  }

  @Post('testWebhook')
  async funTestWebhook(@Body() body, @Query() query) {
    console.log('body', body, 'query', query);
    return {};
  }
}
