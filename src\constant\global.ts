export class GlobalServices {
  static CLOUD_SERVICE: 'GOOGLE' | 'ORACLE' = 'ORACLE';
}

export const SYSTEM_ADMIN_ID = 37;
export const kSystem = 'System';

export const CSE_ROLE_ID = 3;

export const kGlobalDateTrail = 'T10:00:00.000Z';

export const PAGE = 1;
export const PAGE_LIMIT = 10;
export const FILTER_TYPES = {
  SELECTION: 1,
  RANGE: 2,
  DATE: 3,
};

export const QUERY_GRAPHS =
  'SELECT type, title, description, subtitle, row, column, api, method FROM graphs';
export const QUERY_GRAPH_FILTERS =
  'SELECT type, title, defaultValue, otherInfo FROM graphFilters';
export const CHART_GRAPH_DATE_QUERY = `
  SELECT * FROM {{TABLE_NAME}} WHERE graph_date = toDate('{{GRAPH_DATE}}');
`;
