// Imports
import { BankService } from './bank.service';
import { Body, Controller, Post } from '@nestjs/common';

@Controller('bank')
export class BankController {
  constructor(private readonly service: BankService) {}

  @Post('transactions')
  async funTransactions(@Body() body) {
    return await this.service.transactions(body);
  }

  @Post('bulkTransactions')
  async funBulkTramsactions(@Body() body) {
    return await this.service.bulkTransactions(body);
  }
}
