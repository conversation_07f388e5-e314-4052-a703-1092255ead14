// Imports
import { Env } from 'src/config/env';

const nBackendServiceUrl = Env.neighbours.backendService.base_url;
export const nBackendService = {
  experianSoftHit: nBackendServiceUrl + 'experian/getExperianDetails',
  checkFinalApproval: nBackendServiceUrl + 'admin/eligibility/finalApproval',
  finalApprovalList: nBackendServiceUrl + 'admin/verification/finalApproval',
  getTransactionsByLoanId:
    nBackendServiceUrl + 'admin/banking/getTransactionsByLoanId',
};

const nDataCodesBaseUrl = Env.neighbours.dataCodes.base_url;
export const nDataCodes = {
  bulkWrites: nDataCodesBaseUrl + 'v1/database/bulkWrites',
  internalScore: nDataCodesBaseUrl + 'v1/scoring/calculate',
  leadScore: nDataCodesBaseUrl + 'v1/scoring/lead',
  checkPreApproval: nDataCodesBaseUrl + 'v1/scoring/isEligibleForPreApproval',
  readQuery: nDataCodesBaseUrl + 'v1/database/read',
  paymentForecast: nDataCodesBaseUrl + 'v1/payment/forecast',
  asOnDateRecovery: nDataCodesBaseUrl + 'v1/payment/as-on-date-recovery',
};

const nMicroAlertBaseUrl = Env.neighbours.microAlert.base_url;
export const nMicroAlert = {
  sendWaMsg: nMicroAlertBaseUrl + 'whatsApp/send_whatsapp_message',
};

const nTransactionsBaseUrl = Env.neighbours.transactions.base_url;
export const nTransactions = {
  getCompareAccounts: nTransactionsBaseUrl + 'transaction/getCompareAccounts',
};

const nTrueshieldBaseUrl = Env.neighbours.trueShield.base_url;
export const nTrueShield = {
  predictStatement: nTrueshieldBaseUrl + 'bankStatement/predict',
};

const exotelBaseUrl = Env.thirdParty.exotel.base_url;
export const exotelHeaders = {
  Authorization:
    'Basic ' +
    Buffer.from(
      `${Env.thirdParty.exotel.api_key}:${Env.thirdParty.exotel.api_token}`,
    ).toString('base64'),
};
export const exotelUserUrl = `${exotelBaseUrl}${Env.thirdParty.exotel.sid}/users`;
