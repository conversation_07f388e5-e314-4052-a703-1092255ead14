// Imports
import { Module } from '@nestjs/common';
import { ApiService } from './api.service';
import { ObjService } from './obj.service';
import { DateService } from './date.service';
import { EnumService } from './enum.service';
import { UtilsService } from './utils.service';
import { NumberService } from './number.service';
import { UtilsController } from './utils.controller';
import { RedisModule } from 'src/database/redis/redis.module';
import { FileService } from './file.service';

@Module({
  imports: [RedisModule],
  controllers: [UtilsController],
  exports: [
    ApiService,
    DateService,
    FileService,
    EnumService,
    NumberService,
    UtilsService,
  ],
  providers: [
    ApiService,
    DateService,
    ObjService,
    UtilsService,
    FileService,
    EnumService,
    NumberService,
  ],
})
export class UtilsModule {}
