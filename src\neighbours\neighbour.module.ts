// Imports
import { Modu<PERSON> } from '@nestjs/common';
import { DataCodesModule } from './data-codes/data.codes.module';
import { MicroAlertModule } from './micro-alert/micro.alert.module';
import { TransactionsModule } from './transactions/transactions.module';
import { BackendServiceModule } from './backend-service/backend.service.module';

@Module({
  imports: [
    BackendServiceModule,
    DataCodesModule,
    MicroAlertModule,
    TransactionsModule,
  ],
})
export class NeighbourModule {}
