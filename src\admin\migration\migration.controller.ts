// Imports
import { Body, Controller, Post } from '@nestjs/common';
import { MigrationService } from './migration.service';

@Controller('admin/migration')
export class MigrationController {
  constructor(private readonly service: MigrationService) {}

  @Post('updateMissingInternalScore')
  async funUpdateMissingInternalScore(@Body() body) {
    return await this.service.updateMissingInternalScore(body);
  }

  @Post('syncLeadScore')
  async funSyncLeadScore(@Body() body) {
    return await this.service.syncLeadScore(body);
  }
}
