// Imports
import { Module } from '@nestjs/common';
import { CallKaroQuery } from './call.karo.query';
import { PgModule } from 'src/database/pg/pg.module';
import { CallKaroService } from './call.karo.service';
import { CallKaroController } from './call.karo.controller';
import { UtilsModule } from 'src/utils/utils.module';

@Module({
  controllers: [CallKaroController],
  imports: [PgModule, UtilsModule],
  providers: [CallKaroService, CallKaroQuery],
})
export class CallKaroModule {}
