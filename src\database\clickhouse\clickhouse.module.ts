import { Env } from 'src/config/env';
import { Module, Global, Logger } from '@nestjs/common';
import { ClickHouseService } from './clickhouse.service';
import { ClickHouseClient, createClient } from '@clickhouse/client';

@Global()
@Module({
  providers: [
    {
      provide: ClickHouseClient,
      useFactory: () => {
        const client = createClient({
          url: Env.database.clickhouse.url,
          username: Env.database.clickhouse.username,
          password: Env.database.clickhouse.password,
          database: Env.database.clickhouse.database,
        });
        const logger = new Logger('ClickHouseModule');
        logger.log('ClickHouse -> Connected !');
        return client;
      },
    },
    ClickHouseService,
  ],
  exports: [ClickHouseClient, ClickHouseService],
})
export class ClickHouseModule {}
