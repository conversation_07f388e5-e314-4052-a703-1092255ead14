import {
  IsOptional,
  IsDateString,
  Is<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { Type } from 'class-transformer';

export class UserQueryDto {
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @IsOptional()
  @IsDateString()
  endDate?: string;

  @IsOptional()
  @IsString()
  gender?: string; // ENUM: 'Male', 'Female', 'MALE', 'FEMALE'

  @IsOptional()
  @IsString()
  state?: string;

  // CIBIL Score (CibilScoreEntity)
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(300)
  @Max(900)
  startCibil?: number;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(300)
  @Max(900)
  endCibil?: number;

  // PL Score (CibilScoreEntity)
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(300)
  @Max(900)
  startPl?: number;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(300)
  @Max(900)
  endPl?: number;

  // Age (KYCEntity.aadhaarDOB se calculate)
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  startAge?: number;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  endAge?: number;

  // Salary (CibilScoreEntity.monthlyIncome)
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  startSalary?: number;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  endSalary?: number;
}
