// Imports
import { Includeable } from 'sequelize';
import { Injectable } from '@nestjs/common';
import { PgService } from 'src/database/pg/pg.service';
import { MasterEntity } from 'src/database/pg/entities/master.entity';
import { raiseBadRequest, raiseParamMissing } from 'src/config/error';
import { registeredUsers } from 'src/database/pg/entities/registeredUsers';
import { CibilScoreEntity } from 'src/database/pg/entities/cibil.score.entity';

@Injectable()
export class EligibilityQuery {
  constructor(private readonly pg: PgService) {}

  async dataForPreApprovalStatus(reqData) {
    const user_id = reqData.user_id;
    if (!user_id) {
      raiseParamMissing('user_id');
    }

    const masterInc: Includeable = { model: MasterEntity };
    masterInc.attributes = ['otherInfo'];

    const userData: registeredUsers = await this.pg.findOne(registeredUsers, {
      attributes: ['completedLoans'],
      include: [masterInc],
      where: { id: user_id },
    });
    if (!userData) {
      raiseBadRequest('No user data found !');
    }

    const otherInfo = userData.masterData.otherInfo ?? {
      salaryInfo: 0,
      netPaySalary: 0,
    };
    const user_entered_salary = +(
      otherInfo?.salaryInfo ??
      otherInfo?.netPaySalary ??
      0
    );

    const cibil_data = await this.pg.findOne(CibilScoreEntity, {
      attributes: ['id', 'responsedata'],
      order: [['id', 'DESC']],
      where: { status: '1', userId: user_id },
    });
    if (!cibil_data) {
      raiseBadRequest('No CIBIL data found !');
    }

    return {
      completedLoan: userData.completedLoans,
      netPaySalary: user_entered_salary,
      cibil_id: cibil_data.id,
      user_id,
      responsedata: cibil_data.responsedata ?? {},
    };
  }
}
