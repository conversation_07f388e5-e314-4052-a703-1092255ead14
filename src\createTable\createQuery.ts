import { Injectable } from '@nestjs/common';
import { C<PERSON><PERSON>ouseClient } from '@clickhouse/client';
import { ClickHouseService } from 'src/database/clickhouse/clickhouse.service';

@Injectable()
export class CreateQuery {
  constructor(
    private readonly clickhouse: C<PERSON><PERSON>ouseClient,
    private readonly ClickHouseService: ClickHouseService,
  ) {}

  // graphs  table
  async graphsTable(): Promise<void> {
    await this.clickhouse.query({
      query: `
        CREATE TABLE IF NOT EXISTS graphs (
          id Int16,
          type Int16,
          title String,
          description String,
          subtitle String,
          row Int16,
          column Int16,
          api String,
          method String,
          isActive Int16,
          createdAt DateTime DEFAULT now(),
          updatedAt DateTime DEFAULT now()
        ) ENGINE = MergeTree
        ORDER BY id;
      `,
    });
  }

  // graphFilters table
  async graphfilterTable(): Promise<void> {
    await this.clickhouse.query({
      query: `CREATE TABLE IF NOT EXISTS graphFilters (
    id Int16,
    title String,
    defaultValue JSON,
    type Int16, -- 1 = range, 2 = select
    otherInfo JSON,
    isActive Int16,
    createdAt DateTime DEFAULT now(),
    updatedAt DateTime DEFAULT now()
  )
  ENGINE = MergeTree()
  ORDER BY id;
      `,
    });
  }

  // RegisteredUsers table
  async RegisteredUsersTable(): Promise<void> {
    await this.clickhouse.query({
      query: `
      CREATE TABLE IF NOT EXISTS RegisteredUsers (
        id Int16,
        graph_date Date,
        registration_count Int32,
        male_registration Int32,
        female_registration Int32,
        average_salary Int32,
        new_users Int32,
        repeat_users Int32,
        android_users Int32,
        ios_users Int32,
        web_users Int32,
        cse_connect Int32,
        createdAt DateTime DEFAULT now(),
        updatedAt DateTime DEFAULT now()
      ) ENGINE = MergeTree()
      PARTITION BY toYYYYMM(graph_date)
      ORDER BY (graph_date)
    `,
    });
  }

  async UsersStageAnalytics(): Promise<void> {
    await this.clickhouse.query({
      query: `
    CREATE TABLE IF NOT EXISTS UsersStageAnalytics(
    id Int16,
    graph_date Date,
    phone_verification Int32,
    basic_details Int32,
    selfie Int32,
    not_eligible Int32,
    pin Int32,
    aadhaar Int32,
    employement Int32,
    banking Int32,
    loan_accept Int32,
    pan Int32,
    final_verification Int32,
    mandate Int32,
    esign Int32,
    disbursement Int32,
    repayment Int32,
    defaulter Int32,
    re_apply Int32,
    no_route Int32,
    express_reapply Int32,
    on_hold Int32,
    createdAt DateTime DEFAULT now(),
    updatedAt DateTime DEFAULT now()
)
ENGINE = MergeTree()
PARTITION BY toYYYYMM(graph_date)
ORDER BY (graph_date, id);
`,
    });
  }

  async graphId() {
    const query = `SELECT max(id) as lastId FROM graphs`;
    const result = await this.ClickHouseService.injectQuery(query);
    const data = result as { lastId: number | null }[];
    return (data[0]?.lastId ?? 0) + 1;
  }
  async graphfilterId() {
    const query = `SELECT max(id) as lastId FROM graphFilters`;
    const result = await this.ClickHouseService.injectQuery(query);
    const data = result as { lastId: number | null }[];
    return (data[0]?.lastId ?? 0) + 1;
  }

  async registeredUserId() {
    const query = `SELECT max(id) as lastId FROM RegisteredUsers`;
    const result = await this.ClickHouseService.injectQuery(query);
    const data = result as { lastId: number | null }[];
    return (data[0]?.lastId ?? 0) + 1;
  }

  async userStageId() {
    const query = `SELECT max(id) as lastId FROM UsersStageAnalytics`;
    const result = await this.ClickHouseService.injectQuery(query);
    const data = result as { lastId: number | null }[];
    return (data[0]?.lastId ?? 0) + 1;
  }
}
