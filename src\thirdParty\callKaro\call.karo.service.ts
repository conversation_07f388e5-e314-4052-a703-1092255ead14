// Imports
import { Injectable } from '@nestjs/common';
import { CallKaroQuery } from './call.karo.query';
import { DateService } from 'src/utils/date.service';
import { NumberService } from 'src/utils/number.service';

@Injectable()
export class CallKaroService {
  constructor(
    private readonly num: NumberService,
    private readonly query: CallKaroQuery,
    private readonly dateService: DateService,
  ) {}

  async callerData(reqData) {
    const userData: any = await this.query.dataForCallerData(reqData);

    if (userData.loan_amount) {
      userData.loan_amount = this.num.convertNumberToWords(
        +userData.loan_amount,
      );
    }
    if (userData.loan_tenure_in_days) {
      userData.loan_tenure_in_days = this.num.convertNumberToWords(
        +userData.loan_tenure_in_days,
      );
    }
    if (userData.emi_count) {
      userData.emi_count = this.num.convertNumberToWords(+userData.emi_count);
    }
    if (userData.emi_amount) {
      userData.emi_amount = this.num.convertNumberToWords(+userData.emi_amount);
    }
    if (userData.first_emi_date) {
      userData.first_emi_date = this.dateService.formatDateToLongMonth(
        userData.first_emi_date,
      );
      if (userData.interest_rate_per_anuum) {
        userData.interest_rate_per_anuum = this.num.convertFloatToWords(
          +userData.interest_rate_per_anuum,
        );
      }
    }

    return { userData };
  }
}
