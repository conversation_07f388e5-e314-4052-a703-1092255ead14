// Imports
import { Modu<PERSON> } from '@nestjs/common';
import { PgService } from 'src/database/pg/pg.service';
import { CommunicationQuery } from './communication.query';
import { CommunicationService } from './communication.service';
import { CommunicationController } from './communication.controller';
import { MicroAlertModule } from 'src/neighbours/micro-alert/micro.alert.module';

@Module({
  controllers: [CommunicationController],
  imports: [MicroAlertModule],
  providers: [CommunicationQuery, CommunicationService, PgService],
})
export class CommunicationModule {}
