// Imports
import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { LoanQuery } from './loan.query';
import { LoanService } from './loan.service';
import { ApiService } from 'src/utils/api.service';
import { LoanController } from './loan.controller';
import { PgService } from 'src/database/pg/pg.service';
import { UtilsModule } from 'src/utils/utils.module';

@Module({
  controllers: [LoanController],
  imports: [UtilsModule],
  providers: [ApiService, LoanQuery, LoanService, PgService],
})
export class LoanModule {}
