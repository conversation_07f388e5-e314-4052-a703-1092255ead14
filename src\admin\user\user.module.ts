// Imports
import { Module } from '@nestjs/common';
import { UserQuery } from './user.query';
import { UserService } from './user.service';
import { UserController } from './user.controller';
import { PgModule } from 'src/database/pg/pg.module';
import { UtilsModule } from 'src/utils/utils.module';

@Module({
  controllers: [UserController],
  exports: [UserService],
  imports: [PgModule, UtilsModule],
  providers: [UserQuery, UserService],
})
export class UserModule {}
