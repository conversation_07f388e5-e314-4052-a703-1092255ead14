// Imports
import { Module } from '@nestjs/common';
import { LeadQuery } from './lead.query';
import { LeadService } from './lead.service';
import { LeadController } from './lead.controller';
import { PgModule } from 'src/database/pg/pg.module';
import { UtilsModule } from 'src/utils/utils.module';

@Module({
  controllers: [LeadController],
  imports: [PgModule, UtilsModule],
  providers: [LeadService, LeadQuery],
})
export class LeadModule {}
