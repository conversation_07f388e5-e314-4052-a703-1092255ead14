// Imports
import { decryptText } from './crypt';
import { Injectable } from '@nestjs/common';

@Injectable()
export class UtilsService {
  bulkDecryptText(reqData) {
    const finalizedData = {};
    reqData.targetList.forEach((el) => {
      finalizedData[el] = decryptText(el);
    });

    return { success: true, finalizedData };
  }

  removeLeadingZero(phone) {
    return ((phone as string) || '').replace(/^0|\+/g, '');
  }
}
