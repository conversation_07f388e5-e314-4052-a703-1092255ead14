// Imports
import {
  CanActivate,
  ExecutionContext,
  HttpStatus,
  Injectable,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { Env } from 'src/config/env';
import { HTTPError } from 'src/config/error';

// Auth Validation -> All Incoming API Requests from CallKaro Server
@Injectable()
export class CallKaroGuard implements CanActivate {
  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const request = context.switchToHttp().getRequest();
    return true;

    // Get IP address
    const ip =
      request.headers['x-forwarded-for']?.split(',')[0]?.trim() || // if behind proxy
      request.socket?.remoteAddress || // fallback for Node.js
      request.ip; // express fallback
    if (!Env.thirdParty.callKaro.white_listed_ips.includes(ip)) {
      throw new HTTPError({ statusCode: HttpStatus.FORBIDDEN });
    }

    const partner_key = request.headers['partner-key'];
    if (!partner_key) {
      throw new HTTPError({ statusCode: HttpStatus.FORBIDDEN });
    }
    if (partner_key != Env.thirdParty.callKaro.partner_key) {
      throw new HTTPError({ statusCode: HttpStatus.FORBIDDEN });
    }

    return true;
  }
}
