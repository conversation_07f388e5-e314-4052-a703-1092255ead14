// Imports
import { Module } from '@nestjs/common';
import { DataCodesQuery } from './data.codes.query';
import { UtilsModule } from 'src/utils/utils.module';
import { PgService } from 'src/database/pg/pg.service';
import { DataCodesService } from './data.codes.service';
import { DataCodesController } from './data.codes.controller';

@Module({
  controllers: [DataCodesController],
  exports: [DataCodesService, DataCodesQuery],
  imports: [UtilsModule],
  providers: [DataCodesService, DataCodesQuery, PgService],
})
export class DataCodesModule {}
