// Imports
import { Modu<PERSON> } from '@nestjs/common';
import { BureauQuery } from './bureau.query';
import { BureauService } from './bureau.service';
import { BureauController } from './bureau.controller';
import { PgModule } from 'src/database/pg/pg.module';
import { UtilsModule } from 'src/utils/utils.module';
import { BackendServiceModule } from 'src/neighbours/backend-service/backend.service.module';

@Module({
  controllers: [BureauController],
  imports: [BackendServiceModule, PgModule, UtilsModule],
  providers: [BureauQuery, BureauService],
})
export class BureauModule {}
