// Imports
import { Module } from '@nestjs/common';
import { createClient } from 'redis';
import { Logger } from '@nestjs/common';
import { RedisService } from './redis.service';
import { Env } from 'src/config/env';
import { kRedisClient } from 'src/constant/strings';

const getDbConnection = (name: string) => ({
  provide: name,
  useFactory: async () => {
    const client = createClient({
      url: `redis://:${Env.database.redis.password}@${Env.database.redis.host}:${Env.database.redis.port}`,
    });
    const logger = new Logger('RedisModule');
    logger.log('Starting Redis client connection...');
    client.on('error', (err) => logger.error('Redis Connect Error -> ' + err));
    client.on('ready', () => logger.log('Redis client is connected'));
    await client.connect();
    return client;
  },
});

@Module({
  providers: [getDbConnection(kRedisClient), RedisService],
  exports: [RedisService],
})
export class RedisModule {}
