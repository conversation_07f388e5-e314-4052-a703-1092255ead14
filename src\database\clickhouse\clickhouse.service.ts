import { Injectable, OnModuleInit } from '@nestjs/common';
import { ClickHouseClient } from '@clickhouse/client';
import { CLICKHOUSE_USER_DETAILS } from './clickhouse.tables';

@Injectable()
export class ClickHouseService implements OnModuleInit {
  constructor(private readonly client: ClickHouseClient) {}

  onModuleInit() {
    this.injectTables().catch((err) => {
      console.log(err);
    });
  }

  async injectQuery(queryStr: string, jsonResponse = true) {
    const result = await this.client.query({
      query: queryStr,
      format: jsonResponse ? 'JSON' : null,
    });
    if (!jsonResponse) return result;
    // Extract the actual data from the ResultSet
    const rows = await result.json();
    return rows.data ?? [];
  }

  async insertToClickhouse(table: string, data: object) {
    await this.client.insert({
      table,
      values: Array.isArray(data) ? data : [data],
      format: 'JSON',
    });
  }

  private async injectTables() {
    try {
      console.log('Started -> Injecting tables for clickhouse');

      await this.injectQuery(CLICKHOUSE_USER_DETAILS, null);

      console.log('Completed -> Injecting tables for clickhouse');
    } catch (error) {
      console.log(error);
    }
  }
}
