// Imports
import { LeadQuery } from './lead.query';
import { Injectable } from '@nestjs/common';
import { raiseParamMissing } from 'src/config/error';

@Injectable()
export class LeadService {
  constructor(private readonly query: LeadQuery) {}

  async leadPriorityList(reqData) {
    if (!reqData.count) {
      raiseParamMissing('count');
    }
    if (!reqData.adminId) {
      raiseParamMissing('adminId');
    }

    if (reqData.count == 'true') {
      reqData.isCount = true;
    } else if (!reqData.id) {
      raiseParamMissing('id');
    }

    // Count only
    if (reqData.isCount) {
      const data: any = await Promise.allSettled([
        await this.query.dataForHighLeadScore(reqData),
        await this.query.dataForHighLeadScoreUnTouched(reqData),
        await this.query.dataForHighLeadScoreNeverTouched(reqData),
        await this.query.dataForLeftTheAppFewMinsAgo(reqData),
        await this.query.dataForNthTimeAttemptRemain({
          ...reqData,
          crm_attempt: 1,
        }),
        await this.query.dataForNthTimeAttemptRemain({
          ...reqData,
          crm_attempt: 2,
        }),
      ]);

      const values = [
        data[0].value,
        data[1].value,
        data[2].value,
        data[3].value,
        data[4].value,
        data[5].value,
      ];
      const maxValue = Math.max(...values);

      const response = [
        {
          title: 'High Lead Score (Conversion chances: 21%)',
          id: 'HIGH_LEAD_SCORE', // Do not change id, it is assosiated with raw data api
          count: data[0].value,
          percentage: Math.floor((data[0].value * 100) / maxValue),
        },
        {
          title: "Today's Untouched",
          id: 'HIGH_LEAD_SCORE_UNTOUCHED', // Do not change id, it is assosiated with raw data api
          count: data[1].value,
          percentage: Math.floor((data[1].value * 100) / maxValue),
        },
        {
          title: 'Never Touched',
          id: 'HIGH_LEAD_SCORE_NEVER_TOUCHED', // Do not change id, it is assosiated with raw data api
          count: data[2].value,
          percentage: Math.floor((data[2].value * 100) / maxValue),
        },
        {
          title: 'Left the app max 25 minutes ago',
          id: 'LEFT_APP_FEW_MINS_AGO', // Do not change id, it is assosiated with raw data api
          count: data[3].value,
          percentage: Math.floor((data[3].value * 100) / maxValue),
        },
        {
          title: '2nd time Follow Up Required',
          id: 'SECOND_TIME_FOLLOW_UP_REQUIRED', // Do not change id, it is assosiated with raw data api
          count: data[4].value,
          percentage: Math.floor((data[4].value * 100) / maxValue),
        },
        {
          title: '3rd time Follow Up Required',
          id: 'THIRD_TIME_FOLLOW_UP_REQUIRED', // Do not change id, it is assosiated with raw data api
          count: data[5].value,
          percentage: Math.floor((data[5].value * 100) / maxValue),
        },
      ];

      return { data: response };
    }

    // Raw data
    let raw_data;
    switch (reqData.id) {
      case 'HIGH_LEAD_SCORE':
        raw_data = await this.query.dataForHighLeadScore(reqData);
        break;

      case 'HIGH_LEAD_SCORE_UNTOUCHED':
        raw_data = await this.query.dataForHighLeadScoreUnTouched(reqData);
        break;

      case 'HIGH_LEAD_SCORE_NEVER_TOUCHED':
        raw_data = await this.query.dataForHighLeadScoreNeverTouched(reqData);
        break;

      case 'LEFT_APP_FEW_MINS_AGO':
        raw_data = await this.query.dataForLeftTheAppFewMinsAgo(reqData);
        break;

      case 'SECOND_TIME_FOLLOW_UP_REQUIRED':
        reqData.crm_attempt = 1;
        raw_data = await this.query.dataForNthTimeAttemptRemain(reqData);
        break;

      case 'THIRD_TIME_FOLLOW_UP_REQUIRED':
        reqData.crm_attempt = 2;
        raw_data = await this.query.dataForNthTimeAttemptRemain(reqData);
        break;

      default:
        break;
    }

    return { count: raw_data.length, rows: raw_data };
  }
}
