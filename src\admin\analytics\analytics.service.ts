import { Injectable } from '@nestjs/common';
import { Op } from 'sequelize';
import { NUMBERS, UserStage } from 'src/constant/objects';
import { CommonService } from 'src/utils/comman.service';
import { PgService } from 'src/database/pg/pg.service';
import { registeredUsers } from 'src/database/pg/entities/registeredUsers';
import { RedisService } from 'src/database/redis/redis.service';
import {
  FILTER_TYPES,
  QUERY_GRAPH_FILTERS,
  QUERY_GRAPHS,
} from 'src/constant/global';
import { ClickHouseService } from 'src/database/clickhouse/clickhouse.service';
@Injectable()
export class AnalyticsService {
  constructor(
    private readonly commonService: CommonService,
    private readonly pgService: PgService,
    private readonly redisService: RedisService,
    private readonly ClickHouseService: ClickHouseService,
  ) {}

  //#region Dashboard
  async getDashboard() {
    const [graphs, filters] = await Promise.all([
      this.ClickHouseService.injectQuery(QUERY_GRAPHS),
      this.ClickHouseService.injectQuery(QUERY_GRAPH_FILTERS),
    ]);

    const filtersArray = filters as any[];
    const today = new Date().toISOString().split('T')[0];
    const updatedFilters = filtersArray.map((filter) => {
      if (filter.type === FILTER_TYPES.DATE) {
        return {
          ...filter,
          defaultValue: {
            start: today,
            end: today,
          },
        };
      }
      return filter;
    });
    return { filters: updatedFilters, graphs };
  }
  //#endregion

  //#region User Analytics
  async userAnalytics(query) {
    const cacheKey = this.createDynamicRedisKey(query, 'ANALYTICS');
    const today = new Date().toISOString().slice(0, 10);
    const queryDate = query.startDate;
    const isNotToday = queryDate !== today;

    // Check ClickHouse first for past dates
    if (isNotToday) {
      const clickHouseData = await this.getClickHouseData(query);
      // If ClickHouse has data, return it
      if (
        clickHouseData.stageSummary.some((item) => Number(item.yKey) > 0) ||
        clickHouseData.registrationSummary.some((item) => Number(item.yKey) > 0)
      ) {
        console.log('Data found in ClickHouse, returning it');
        return clickHouseData;
      }
      console.log('Data not found in ClickHouse, fetching from PostgreSQL');
    } else {
      const cachedData = await this.redisService.get(cacheKey);
      if (cachedData) {
        return JSON.parse(cachedData).result;
      }
    }

    // Fetch fresh data from PostgreSQL with applied filters
    const where = await this.buildWhereFilter(query);
    const stageRows = await this.pgService.findAll(registeredUsers, {
      attributes: ['stage', 'gender', 'typeOfDevice', 'completedLoans'],
      where,
      raw: true,
    });

    // Initialize stage and process stage data
    const stage = {};
    for (const key in UserStage) stage[key] = 0;

    for (const row of stageRows) {
      const stageKey = Object.keys(UserStage).find(
        (k) => UserStage[k] == row.stage,
      );
      if (stageKey) stage[stageKey]++;
    }

    const stageSummary = Object.entries(stage).map(([xKey, yKey]) => ({
      xKey: this.commonService.toPascalCase(xKey),
      yKey,
    }));

    // Initialize registration
    let registrationCount = 0,
      male = 0,
      female = 0,
      android = 0,
      ios = 0,
      web = 0,
      newUsers = 0,
      repeatUsers = 0;

    // Process registration analytics
    for (const { gender, typeOfDevice, completedLoans } of stageRows) {
      registrationCount++;

      if (gender === 'MALE') male++;
      else if (gender === 'FEMALE') female++;

      if (typeOfDevice === '0') android++;
      else if (typeOfDevice === '1') ios++;
      else if (typeOfDevice === '2') web++;

      if (completedLoans === 0) newUsers++;
      else if (completedLoans > 0) repeatUsers++;
    }

    const registrationSummary = [
      { xKey: 'Registration Count', yKey: registrationCount },
      { xKey: 'Male Registration', yKey: male },
      { xKey: 'Female Registration', yKey: female },
      { xKey: 'Android Users', yKey: android },
      { xKey: 'IOS Users', yKey: ios },
      { xKey: 'Web Users', yKey: web },
      { xKey: 'New Users', yKey: newUsers },
      { xKey: 'Repeat Users', yKey: repeatUsers },
    ];

    const result = { stageSummary, registrationSummary };

    console.log('isNotToday:', isNotToday, 'queryDate:', queryDate);

    if (isNotToday) {
      const stageRowObj = this.transformSummaryToClickHouseFormat(
        result.stageSummary,
        queryDate,
      );
      const registrationRowObj = this.transformSummaryToClickHouseFormat(
        registrationSummary,
        queryDate,
      );
      await Promise.all([
        this.ClickHouseService.insertToClickhouse(
          'UsersStageAnalytics',
          stageRowObj,
        ),
        this.ClickHouseService.insertToClickhouse(
          'RegisteredUsers',
          registrationRowObj,
        ),
      ]);
      console.log(
        'Inserted to ClickHouse' + queryDate,
        result,
        stageRowObj,
        registrationRowObj,
      );
    } else {
      await this.redisService.set(
        cacheKey,
        JSON.stringify({ result }),
        NUMBERS.THREE_HOURS_IN_SECONDS,
      );
    }

    return result;
  }
  //#endregion

  async getClickHouseData(query) {
    const { startDate, endDate } = this.commonService.getUTCDateRange(
      query.startDate,
      query.endDate,
    );

    // Extract date part (YYYY-MM-DD) from ISO datetime strings for ClickHouse toDate() function
    const startDateOnly = startDate.split('T')[0];
    const endDateOnly = endDate.split('T')[0];

    // Fetch data from ClickHouse
    const [registeredData, stageData] = await Promise.all([
      this.ClickHouseService.injectQuery(
        `SELECT * FROM RegisteredUsers WHERE graph_date BETWEEN toDate('${startDateOnly}') AND toDate('${endDateOnly}')`,
      ),
      this.ClickHouseService.injectQuery(
        `SELECT * FROM UsersStageAnalytics WHERE graph_date BETWEEN toDate('${startDateOnly}') AND toDate('${endDateOnly}')`,
      ),
    ]);
    const stageSummary = this.processClickHouseData(stageData);
    const registrationSummary = this.processClickHouseData(registeredData);
    console.log('Data found in ClickHouse, returning it');
    return { stageSummary, registrationSummary };
  }

  private processClickHouseData(data) {
    const summary = {};
    for (const row of data) {
      for (const key in row) {
        if (key !== 'graph_date' && key !== 'id') {
          const xKey = this.commonService.toPascalCase(key);
          summary[xKey] = (summary[xKey] ?? 0) + Number(row[key]);
        }
      }
    }
    return Object.entries(summary).map(([xKey, yKey]) => ({ xKey, yKey }));
  }

  private transformSummaryToClickHouseFormat(
    summaryArray: Array<{ xKey: string; yKey: any }>,
    queryDate: string,
  ) {
    return summaryArray.reduce(
      (acc, item) => {
        const key = item.xKey.replace(/\s+/g, '_').toLowerCase();
        acc[key] = item.yKey;
        return acc;
      },
      { graph_date: queryDate },
    );
  }

  // stage summary
  async getUserStage(query) {
    const { stageSummary } = await this.userAnalytics(query);
    return stageSummary;
  }

  // registration summary
  async getUserStageCount(query) {
    const { registrationSummary } = await this.userAnalytics(query);
    return registrationSummary;
  }

  //#region Filter Analytics
  private async buildWhereFilter(query) {
    const { startDate, endDate } = this.commonService.getUTCDateRange(
      query.startDate,
      query.endDate,
    );
    const where: any = {};

    // Basic filters
    if (startDate && endDate) {
      where.createdAt = { [Op.gte]: startDate, [Op.lte]: endDate };
    }
    if (query.gender) where.gender = query.gender;
    if (query.state) where.state = query.state;

    let userIds: string[] | null = null;

    // CIBIL/PL/Salary filter
    if (
      query?.startCibil ||
      query?.endCibil ||
      query?.startPl ||
      query?.endPl ||
      query?.startSalary ||
      query?.endSalary
    ) {
      const scoreWhere: any = {};

      if (query?.startCibil || query?.endCibil) {
        scoreWhere.cibilScore = {};
        if (query?.startCibil) scoreWhere.cibilScore[Op.gte] = query.startCibil;
        if (query?.endCibil) scoreWhere.cibilScore[Op.lte] = query.endCibil;
      }
      if (query?.startPl || query?.endPl) {
        scoreWhere.plScore = {};
        if (query?.startPl) scoreWhere.plScore[Op.gte] = query.startPl;
        if (query?.endPl) scoreWhere.plScore[Op.lte] = query.endPl;
      }
      if (query?.startSalary || query?.endSalary) {
        scoreWhere.monthlyIncome = {};
        if (query?.startSalary)
          scoreWhere.monthlyIncome[Op.gte] = query.startSalary;
        if (query?.endSalary)
          scoreWhere.monthlyIncome[Op.lte] = query.endSalary;
      }

      const cibilUsers = await this.pgService.findAll('CibilScoreEntity', {
        attributes: ['userId'],
        where: scoreWhere,
        raw: true,
      });

      userIds = cibilUsers.map((u) => u.userId);
    }

    // Age filter
    if (query?.startAge || query?.endAge) {
      const kycUsers = await this.pgService.findAll('KYCEntity', {
        attributes: ['userId', 'aadhaarDOB'],
        where: { aadhaarDOB: { [Op.ne]: null, [Op.not]: '' } },
        raw: true,
      });

      const currentYear = new Date().getFullYear();
      const ageUserIds = kycUsers
        .filter(({ aadhaarDOB }) => {
          let year: number | null = null;
          if (/^\d{2}\/\d{2}\/\d{4}$/.test(aadhaarDOB))
            year = parseInt(aadhaarDOB.split('/')[2], 10);
          else if (/^\d{2}-\d{2}-\d{4}$/.test(aadhaarDOB))
            year = parseInt(aadhaarDOB.split('-')[2], 10);
          else if (/^\d{4}-\d{2}-\d{2}$/.test(aadhaarDOB))
            year = parseInt(aadhaarDOB.split('-')[0], 10);

          if (!year) return false;
          const age = currentYear - year;
          if (query?.startAge && age < query.startAge) return false;
          if (query?.endAge && age > query.endAge) return false;
          return true;
        })
        .map((u) => u.userId);

      userIds = userIds
        ? userIds.filter((id) => ageUserIds.includes(id))
        : ageUserIds;
    }

    if (userIds !== null) {
      where.id = userIds.length > 0 ? { [Op.in]: userIds } : null;
    }

    return where;
  }
  //#endregion

  //#region Dynamic Redis Key
  private createDynamicRedisKey(query, baseKey: string): string {
    const normalized = {
      gender: query.gender ? query?.gender.toUpperCase() : null,
      state: query.state ? query?.state.toUpperCase() : null,
      age: {
        start: query?.startAge ?? '',
        end: query?.endAge ?? '',
      },
      cibil: {
        start: query?.startCibil ?? '',
        end: query?.endCibil ?? '',
      },
      pl: {
        start: query?.startPl ?? '',
        end: query?.endPl ?? '',
      },
      salary: {
        start: query?.startSalary ?? '',
        end: query?.endSalary ?? '',
      },
    };
    return `${baseKey}:${JSON.stringify(normalized)}`;
  }
  //#endregion
}
