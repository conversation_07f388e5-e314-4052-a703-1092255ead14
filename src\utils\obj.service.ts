// Imports
import { Injectable } from '@nestjs/common';

@Injectable()
export class ObjService {
  deleteKey<PERSON><PERSON><PERSON>son(obj: any, keyToDelete: string): any {
    if (typeof obj !== 'object' || obj === null) {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map((item) => this.deleteKeyFromJson(item, keyToDelete));
    }

    const newObj: any = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        if (key === keyToDelete) {
          continue; // Skip the key to delete
        }
        newObj[key] = this.deleteKey<PERSON>rom<PERSON>son(obj[key], keyToDelete);
      }
    }

    return newObj;
  }

  splitToNChunks(array, n) {
    var arrays = [];

    for (let i = 0; i < array.length; i += n)
      arrays.push(array.slice(i, i + n));

    return arrays;
  }
}
