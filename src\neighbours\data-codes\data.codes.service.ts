// Imports
import { Env } from 'src/config/env';
import { HttpStatusCode } from 'axios';
import { HttpStatus, Injectable } from '@nestjs/common';
import { ApiService } from 'src/utils/api.service';
import { nDataCodes } from 'src/constant/networks';
import { DataCodesQuery } from './data.codes.query';
import { DateService } from 'src/utils/date.service';
import { HTTPError, raiseParamMissing } from 'src/config/error';
import { kGlobalDateTrail } from 'src/constant/global';
import { DATA_CODES_CLICKHOUSE_TABLE, kEMIEnums } from 'src/constant/objects';
import { getDataCodesAuth } from 'src/constant/auth';

const acc_list = [];

const headers = getDataCodesAuth();

@Injectable()
export class DataCodesService {
  constructor(
    private readonly api: ApiService,
    private readonly query: DataCodesQuery,
    private readonly dateService: DateService,
  ) {}

  async calculateInternalScore(reqData) {
    const loanId = reqData.loanId;
    if (!loanId) raiseParamMissing('loanId');
    const isReadOnly = reqData.readOnly == 'true' || reqData.readOnly == true;

    const loanData = await this.query.dataForInternalScore(loanId);

    const responseData: any = loanData.cibilData.responsedata ?? '{}';
    const consumerCreditData = (responseData.consumerCreditData ?? [])[0];
    const creditAccounts: any[] = consumerCreditData.accounts ?? [];

    let isActiveEducationLoan = false;
    let isActiveHomeLoan = false;
    let isActivePropertyLoan = false;
    let highCreditAmount = 0;

    for (let index = 0; index < creditAccounts.length; index++) {
      const accData = creditAccounts[index];
      if (accData.dateOpened) {
        creditAccounts[index].dateOpened = this.dateService.strToDate(
          accData.dateOpened,
          'DDMMYYYY',
        );
      }

      // Check Active Loan
      if (!accData.dateClosed) {
        highCreditAmount += accData?.highCreditAmount ?? 0;

        if (accData.accountType == '03') {
          isActivePropertyLoan = true;
        } else if (accData.accountType == '08' || accData.accountType == '47') {
          isActiveEducationLoan = true;
        } else if (accData.accountType == '02' || accData.accountType == '42') {
          isActiveHomeLoan = true;
        }
      }
    }

    creditAccounts.sort(
      (a, b) => a.dateOpened.getTime() - b.dateOpened.getTime(),
    );
    const firstOpenedDate = creditAccounts[0].dateOpened;
    const cibilFetchDate = this.dateService.strToDate(
      consumerCreditData.scores[0]?.scoreDate,
      'DDMMYYYY',
    );
    const diffInYears = this.dateService.difference(
      firstOpenedDate,
      cibilFetchDate,
      'Years',
    );

    const body = {
      loanId,
      completedLoan: loanData.completedLoan,
      cibilScore: loanData.cibilData.cibilScore,
      plScore: loanData.cibilData.plScore,
      PLOutstanding: loanData.cibilData.PLOutstanding,
      PLAccounts: loanData.cibilData.PLAccounts,
      totalOverdueDays: loanData.cibilData.totalOverdueDays,
      overdueBalance: loanData.cibilData.overdueBalance,
      inquiryPast30Days: loanData.cibilData.inquiryPast30Days,
      highCreditAmount,
      zeroBalanceAccounts: loanData.cibilData.zeroBalanceAccounts,
      DeviceType:
        loanData.initialTypeOfDevice == '1'
          ? 'iOS'
          : loanData.initialTypeOfDevice == '2'
            ? 'Web'
            : 'Android',
      State: loanData.aadhaarState,
      salary: loanData.bankingData.salary ?? loanData.bankingData.adminSalary,
      Age: loanData.dobInYears,
      currentBalance: loanData.cibilData.currentBalance,
      creditAge: diffInYears,
      open_housing_loan: isActiveHomeLoan ? 'Yes' : 'No',
      open_property_loan: isActivePropertyLoan ? 'Yes' : 'No',
      open_education_loan: isActiveEducationLoan ? 'Yes' : 'No',
    };

    // Purpose -> Debugging
    if (isReadOnly) {
      return { isReadOnly, body };
    }

    const token = Buffer.from(
      `${Env.neighbours.dataCodes.username}:${Env.neighbours.dataCodes.password}`,
      'utf8',
    ).toString('base64');
    const headers = { Authorization: `Basic ${token}` };

    const response = await this.api.post(
      nDataCodes.internalScore,
      body,
      null,
      null,
      { headers },
    );
    if (response.status != HttpStatusCode.Ok) {
      throw HTTPError({});
    }
    // Success
    return response.response?.output ?? {};
  }

  async syncEMIDetails(reqData) {
    const targetData = await this.query.dataForSyncEMIDetails(reqData);
    const isReadOnly = reqData.readOnly == true || reqData.readOnly == 'true';

    const rows = [];

    const today = this.dateService.getTodayGlobalDate();
    const todayTime = today.getTime();
    for (let index = 0; index < targetData.emiList.length; index++) {
      const emiData = targetData.emiList[index];
      const emiDate = new Date(emiData.emi_date);
      const emiId = emiData.id;

      const isUpcomingEMI = emiDate.getTime() > todayTime;
      let isPaid =
        emiData.payment_done_date != null &&
        emiData.payment_done_date != undefined;
      let isDefaulter = !isPaid && !isUpcomingEMI;
      let emi_status = kEMIEnums.UPCOMING;
      let emiPaidDate: Date | undefined = undefined;
      if (isPaid) {
        emiPaidDate = new Date(emiData.payment_done_date);
      }
      if (isDefaulter) {
        emi_status = kEMIEnums.DEFAULTER;
      } else if (!isUpcomingEMI) {
        if (emiDate.getTime() > emiPaidDate.getTime()) {
          emi_status = kEMIEnums.PREPAID;
        } else if (emiDate.getTime() == emiPaidDate.getTime()) {
          emi_status = kEMIEnums.ONTIME;
        } else if (emiDate.getTime() < emiPaidDate.getTime()) {
          emi_status = kEMIEnums.DELAYED;
        }
      }

      const exp_principal =
        emi_status == kEMIEnums.UPCOMING ? 0 : emiData.principalCovered;
      let till_date_paid_principal = 0;
      let till_dpd_5_paid_principal = 0;

      const transList = targetData.transactionData[emiId] ?? [];
      const dpd_5_date = new Date(emiDate);
      dpd_5_date.setDate(dpd_5_date.getDate() + 5);
      for (let index = 0; index < transList.length; index++) {
        till_date_paid_principal += transList[index].principalAmount ?? 0;

        const paid_date = new Date(transList[index].completionDate);
        if (paid_date.getTime() <= dpd_5_date.getTime()) {
          till_dpd_5_paid_principal += transList[index].principalAmount ?? 0;
        }
      }

      // Calculation -> Full pay
      if (emiData.pay_type == 'FULLPAY') {
        till_date_paid_principal += emiData.fullPayPrincipal ?? 0;

        if (emiPaidDate.getTime() <= dpd_5_date.getTime()) {
          till_dpd_5_paid_principal += emiData.fullPayPrincipal ?? 0;
        }
      }

      // Handle refundable amount
      if (till_date_paid_principal > exp_principal) {
        till_date_paid_principal = exp_principal;
      }

      const loanData = targetData.loanData[emiData.loanId] ?? {};
      const cibilData = targetData.cibilData[loanData.cibilId] ?? {};

      rows.push({
        emi_id: emiId,
        emi_date: emiDate.toJSON().substring(0, 10),
        emi_paid_date: emiPaidDate
          ? emiPaidDate.toJSON().substring(0, 10)
          : null,
        emi_status,
        emi_number: emiData.emiNumber,
        part_of_emi: emiData.partOfemi,
        loanId: emiData.loanId,
        exp_principal,
        till_date_paid_principal,
        till_dpd_5_paid_principal,
        cibil_score: cibilData.cibilScore ?? -2,
        pl_score: cibilData.plScore ?? -2,
        completed_loan: loanData.completedLoan ?? -2,
        inquiry_in_30_days: cibilData.inquiryPast30Days ?? -2,
      });
    }

    if (isReadOnly) {
      return { count: rows.length, rows: rows, isReadOnly };
    }

    const response = await this.api.post(nDataCodes.bulkWrites, {
      db_type: 'clickHouse',
      table_name: 'emi_details',
      objects: rows,
    });
    return { isReadOnly, response };
  }

  async syncAllEMIDetails() {
    const yesterday = this.dateService.getTodayGlobalDate();
    yesterday.setDate(yesterday.getDate() - 1);

    let endDate = new Date(yesterday);
    let startDate = new Date(endDate);
    startDate.setDate(startDate.getDate() - 10);

    let canRun = true;
    while (canRun) {
      // End of loop
      if (endDate.getFullYear() < 2024) {
        canRun = false;
        break;
      }

      // Last adjustment
      if (startDate.getFullYear() < 2024) {
        startDate = new Date('2024-01-01' + kGlobalDateTrail);
      }

      const body = {
        startDate: startDate.toJSON().substring(0, 10),
        endDate: endDate.toJSON().substring(0, 10),
      };
      await this.syncEMIDetails(body);

      endDate = new Date(startDate);
      endDate.setDate(endDate.getDate() - 1);
      startDate = new Date(endDate);
      startDate.setDate(startDate.getDate() - 10);
    }

    return {};
  }

  async syncCIBILDetails() {
    let page = 1;
    let pageSize = 100;

    let canRun = true;
    while (canRun) {
      const cibilData = await this.query.dataForSyncCIBILDetails({
        page,
        pageSize,
      });

      const sub_list = cibilData.rows.filter(
        (el) => el.collateral_type == '03' && el.account_type == '04',
      );
      if (sub_list.length > 0) {
        acc_list.push(...sub_list);
      }

      // Preparation -> API Body-> Data-Codes
      // const body = {
      //   db_type: 'clickHouse',
      //   objects: cibilData.rows,
      //   table_name: 'cibil_acc_details',
      // };
      // // Hit -> API -> Data-Codes
      // await this.api.post(nDataCodes.bulkWrites, body, null, null, {
      //   headers: getDataCodesAuth(),
      // });

      // Break on last span
      if (cibilData.cibil_list_count < pageSize) {
        canRun = false;
        break;
      }

      page++;
    }

    return { acc_list };
  }

  async injectReadOnlyQuery(reqData) {
    const query: string = reqData.query;
    if (!query) {
      raiseParamMissing('query');
    }
    if (!query.includes(DATA_CODES_CLICKHOUSE_TABLE.experian_soft_eligible)) {
      throw HTTPError({ value: query });
    }
    const db_type: string = reqData.db_type;
    if (!db_type) {
      raiseParamMissing('db_type');
    }
    if (db_type != 'clickHouse') {
      throw HTTPError({ value: 'db_type' });
    }

    const body = {
      db_type,
      query,
    };
    const response = await this.api.post(
      nDataCodes.readQuery,
      body,
      null,
      null,
      { headers: getDataCodesAuth() },
    );
    if (response.status == HttpStatusCode.Ok) {
      return response.response.output ?? [];
    } else {
      throw HTTPError({
        message: 'Failed Data codes api -> ' + nDataCodes.readQuery,
      });
    }
  }

  testList() {
    return acc_list;
  }

  async paymentForecast() {
    const response = await this.api.get(
      nDataCodes.paymentForecast,
      {},
      headers,
    );
    if (response && response.status == HttpStatus.OK) {
      return response.response.data ?? {};
    } else {
      throw HTTPError({ message: 'Error while API -> paymentForecast' });
    }
  }

  async asOnDateRecovery() {
    const response = await this.api.get(
      nDataCodes.asOnDateRecovery,
      {},
      headers,
    );
    if (response && response.status == HttpStatus.OK) {
      return response.response.data ?? {};
    } else {
      throw HTTPError({ message: 'Error while API -> paymentForecast' });
    }
  }
}
