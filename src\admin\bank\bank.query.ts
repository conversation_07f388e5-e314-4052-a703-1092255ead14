// Imports

import { Includeable, Op } from 'sequelize';
import { PgService } from 'src/database/pg/pg.service';
import { HttpStatus, Injectable } from '@nestjs/common';
import { HTTPError, raiseParamMissing } from 'src/config/error';
import { BankingEntity } from 'src/database/pg/entities/banking.entity';
import { loanTransaction } from 'src/database/pg/entities/loanTransaction';

@Injectable()
export class BankQuery {
  constructor(private readonly pg: PgService) {}

  async getDataForTransactions(loanId) {
    const bankInclude: Includeable = {
      attributes: ['accountNumber', 'bank'],
      model: BankingEntity,
    };

    const loanAttr = ['id'];
    const loanOptions = {
      include: [bankInclude],
      attributes: loanAttr,
      where: { id: loanId },
    };
    const loanData = await this.pg.findOne(loanTransaction, loanOptions);
    if (!loanData) {
      throw new HTTPError({
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'No loan data found !',
      });
    }

    return loanData;
  }

  async getDataForBulkTransactions(reqData) {
    const source: 'DISBURSEMENT' = reqData.source;
    if (!source) {
      raiseParamMissing('source');
    }
    const start_date = reqData.start_date;
    if (!start_date) {
      raiseParamMissing('start_date');
    }
    const end_date = reqData.end_date;
    if (!end_date) {
      raiseParamMissing('end_date');
    }

    if (source == 'DISBURSEMENT') {
      const loanList: loanTransaction[] = await this.pg.findAll(
        loanTransaction,
        {
          attributes: ['id'],
          where: {
            loan_disbursement_date: {
              [Op.gte]: start_date,
              [Op.lte]: end_date,
            },
          },
        },
      );
      return loanList.map((el) => el.id);
    }

    return {};
  }
}
