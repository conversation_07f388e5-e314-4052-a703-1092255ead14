// Imports
import {
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
} from 'sequelize-typescript';
import { registeredUsers } from './registeredUsers';
import { loanTransaction } from './loanTransaction';

@Table({})
export class EmiEntity extends Model<EmiEntity> {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
    allowNull: false,
  })
  id: number;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  emi_date: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: null,
  })
  emiNumber: Number;

  @Column({
    type: DataType.ENUM,
    allowNull: true,
    values: ['FIRST', 'SECOND', 'LAST'],
    defaultValue: null,
  })
  partOfemi: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  payment_done_date: string;

  @Column({
    type: DataType.ENUM,
    values: ['0', '1', '2'],
    comment: '0=Pending,1=Done,2=Rejected',
    allowNull: false,
    defaultValue: '0',
  })
  payment_status: string;

  @Column({
    type: DataType.TEXT,
    comment: '0=Payment not Due,1=Payment Due',
    allowNull: true,
  })
  payment_due_status: string;

  @ForeignKey(() => registeredUsers)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  userId: string;

  @BelongsTo(() => registeredUsers)
  user: registeredUsers;

  @ForeignKey(() => loanTransaction)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  loanId: number;

  @BelongsTo(() => loanTransaction)
  loan: loanTransaction;

  @Column({
    type: DataType.DOUBLE,
    allowNull: true,
    defaultValue: 0.0,
  })
  principalCovered: number;

  @Column({
    type: DataType.DOUBLE,
    allowNull: true,
    defaultValue: 0.0,
  })
  interestCalculate: number;

  @Column({
    type: DataType.ENUM,
    values: ['FULLPAY', 'EMIPAY', 'PARTPAY', 'SETTLEDPAY'],
    allowNull: true,
  })
  pay_type: string;

  @Column({
    type: DataType.DOUBLE,
    allowNull: true,
    defaultValue: 0,
  })
  fullPayPrincipal: number;

  @Column({
    type: DataType.DOUBLE,
    allowNull: true,
    defaultValue: 0,
  })
  fullPayInterest: number;
}
