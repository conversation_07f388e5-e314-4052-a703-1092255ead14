// Imports
import { BankQuery } from './bank.query';
import { Injectable } from '@nestjs/common';
import { HTTPError } from 'src/config/error';
import { getDataCodesAuth } from 'src/constant/auth';
import { nDataCodes } from 'src/constant/networks';
import { TransactionsService } from 'src/neighbours/transactions/transactions.service';
import { ApiService } from 'src/utils/api.service';
import { logInfo } from 'src/utils/consoles';
import { ObjService } from 'src/utils/obj.service';

@Injectable()
export class BankService {
  constructor(
    private readonly api: ApiService,
    private readonly query: BankQuery,
    private readonly objService: ObjService,
    private readonly neighbourTrans: TransactionsService,
  ) {}

  async transactions(reqData) {
    const loanId = reqData.loanId;
    if (!loanId) {
      throw HTTPError({ parameter: 'loanId' });
    }
    const excludedParams = reqData.excludedParams ?? [];
    const includedParams = reqData.includedParams ?? [];

    const loanData = await this.query.getDataForTransactions(loanId);
    const bankingData = loanData.bankingData ?? {};

    const transResponse = await this.neighbourTrans.getInsights({
      accountNumber: bankingData.accountNumber,
      bankCode: bankingData.bank,
      excludedParams,
      includedParams,
      loanId,
      refreshTsTagging: reqData.refreshTsTagging,
      returnWrongTags: reqData.returnWrongTags,
    });
    return transResponse;
  }

  async bulkTransactions(reqData) {
    const loanIds = await this.query.getDataForBulkTransactions(reqData);

    const subList = this.objService.splitToNChunks(loanIds, 10); // Splitting big array into smaller chunks for Promise.allSettled

    for (let index = 0; index < subList.length; index++) {
      try {
        const spitttedList = subList[index];
        const promiseList = [];
        for (let i = 0; i < spitttedList.length; i++) {
          try {
            promiseList.push(
              this.transactions({
                loanId: spitttedList[i],
                excludedParams: reqData.excludedParams,
                includedParams: reqData.includedParams,
                refreshTsTagging: reqData.refreshTsTagging,
              }),
            );
          } catch (error) {
            logInfo(`Error in sub iteration ${error}`);
          }
        }

        // Do not use promise.all as in promise.all one request fails all are fails and no response is received
        const promiseResult: any = (
          await Promise.allSettled(promiseList)
        ).filter((el) => el.status == 'fulfilled');
        const subTransactions = promiseResult.map((el) => el.value);
        const objects = [];
        subTransactions.forEach((el) => {
          objects.push(...el.transactions);
        });
        await this.api.post(
          nDataCodes.bulkWrites,
          {
            db_type: 'clickHouse',
            table_name: 'bank_transaction_details',
            objects,
          },
          null,
          null,
          { headers: getDataCodesAuth() },
        );
      } catch (error) {
        logInfo(`Error in main iteration ${error}`);
      }
    }

    return {};
  }
}
