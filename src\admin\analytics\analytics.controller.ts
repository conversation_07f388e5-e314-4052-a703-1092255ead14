import { Controller, Get, Query } from '@nestjs/common';
import { AnalyticsService } from './analytics.service';
import { UserQueryDto } from './entity/dto/userQuery.dto';

@Controller('analytics')
export class AnalyticsController {
  constructor(private readonly service: AnalyticsService) {}

  // Dashboard API
  @Get()
  async getDashboard() {
    return await this.service.getDashboard();
  }

  @Get('stage')
  getUserStage(@Query() query: UserQueryDto) {
    return this.service.getUserStage(query);
  }
  // Registered User
  @Get('register')
  async getRegisteredUserData(@Query() query: UserQueryDto) {
    return this.service.getUserStageCount(query);
  }
  // User Stage Count
  @Get('analytics')
  getUserStageCount(@Query() query: UserQueryDto) {
    return this.service.getClickHouseData(query);
  }
}
