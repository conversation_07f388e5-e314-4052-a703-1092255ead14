// Imports
import axios from 'axios';
import { Injectable } from '@nestjs/common';

@Injectable()
export class ApiService {
  async get(url: string, params = {}, headers = {}) {
    const response = await axios.get(url, { params, headers });
    return { response: response.data, status: response.status };
  }

  async post(
    url: string,
    body: any = {},
    headers: any = {},
    auth: any = {},
    options: any = {},
    timeout = 180000,
  ) {
    try {
      const response = await axios.post(url, body, {
        headers,
        auth,
        ...options,
        timeout,
      });

      return { response: response.data, status: response.status };
    } catch (error) {
      return {
        response: error.response?.data,
        statusCode: error.response?.status,
      };
    }
  }
}
